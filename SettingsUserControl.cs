using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class SettingsUserControl : UserControl
    {
        private Panel storeInfoPanel;
        private Panel systemPanel;
        private Panel backupPanel;
        private TextBox storeNameTextBox;
        private TextBox phoneTextBox;
        private TextBox addressTextBox;
        private TextBox emailTextBox;
        private TextBox currencySymbolTextBox;
        private PictureBox logoPreviewPictureBox;
        private Button selectLogoButton;
        private Button removeLogoButton;
        private Button saveStoreInfoButton;
        private Button backupButton;
        private Button restoreButton;
        private Button clearDataButton;
        private Label dbLocationLabel;
        private Label dbSizeLabel;
        private Label lastBackupLabel;

        public SettingsUserControl()
        {
            InitializeComponent();
            LoadStoreSettings();
            LoadSystemInfo();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateStoreInfoPanel();
            CreateSystemPanel();
            CreateBackupPanel();

            this.Controls.AddRange(new Control[] { storeInfoPanel, systemPanel, backupPanel });
        }

        private void CreateStoreInfoPanel()
        {
            storeInfoPanel = new Panel
            {
                Size = new Size(980, 280),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "معلومات المحل",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Row 1
            var storeNameLabel = new Label
            {
                Text = "اسم المحل:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            storeNameTextBox = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(300, 23)
            };

            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(450, 50),
                AutoSize = true
            };

            phoneTextBox = new TextBox
            {
                Location = new Point(540, 47),
                Size = new Size(200, 23)
            };

            // Row 2
            var addressLabel = new Label
            {
                Text = "العنوان:",
                Location = new Point(10, 90),
                AutoSize = true
            };

            addressTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(400, 23)
            };

            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(550, 90),
                AutoSize = true
            };

            emailTextBox = new TextBox
            {
                Location = new Point(670, 87),
                Size = new Size(200, 23)
            };

            // Row 3
            var currencyLabel = new Label
            {
                Text = "رمز العملة:",
                Location = new Point(10, 130),
                AutoSize = true
            };

            currencySymbolTextBox = new TextBox
            {
                Location = new Point(120, 127),
                Size = new Size(100, 23),
                Text = "د.إ"
            };

            // Logo section
            var logoLabel = new Label
            {
                Text = "شعار المحل:",
                Location = new Point(10, 170),
                AutoSize = true
            };

            logoPreviewPictureBox = new PictureBox
            {
                Location = new Point(120, 167),
                Size = new Size(100, 80),
                BorderStyle = BorderStyle.FixedSingle,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            selectLogoButton = new Button
            {
                Text = "اختيار شعار",
                Location = new Point(240, 170),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            selectLogoButton.Click += SelectLogoButton_Click;

            removeLogoButton = new Button
            {
                Text = "إزالة الشعار",
                Location = new Point(350, 170),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            removeLogoButton.Click += RemoveLogoButton_Click;

            saveStoreInfoButton = new Button
            {
                Text = "حفظ معلومات المحل",
                Location = new Point(120, 210),
                Size = new Size(150, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            saveStoreInfoButton.Click += SaveStoreInfoButton_Click;

            storeInfoPanel.Controls.AddRange(new Control[] {
                titleLabel, storeNameLabel, storeNameTextBox,
                phoneLabel, phoneTextBox, addressLabel, addressTextBox,
                emailLabel, emailTextBox, currencyLabel, currencySymbolTextBox,
                logoLabel, logoPreviewPictureBox, selectLogoButton, removeLogoButton,
                saveStoreInfoButton
            });
        }

        private void SelectLogoButton_Click(object sender, EventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "Image Files (*.png;*.jpg;*.jpeg;*.bmp;*.gif)|*.png;*.jpg;*.jpeg;*.bmp;*.gif",
                    Title = "اختر صورة الشعار"
                };

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    var logoImage = Image.FromFile(openDialog.FileName);
                    logoPreviewPictureBox.Image = logoImage;
                    logoPreviewPictureBox.Tag = openDialog.FileName; // Store file path
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الصورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RemoveLogoButton_Click(object sender, EventArgs e)
        {
            logoPreviewPictureBox.Image = null;
            logoPreviewPictureBox.Tag = null;
        }

        private void CreateSystemPanel()
        {
            systemPanel = new Panel
            {
                Size = new Size(980, 150),
                Location = new Point(10, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "معلومات النظام",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            dbLocationLabel = new Label
            {
                Text = "موقع قاعدة البيانات: جاري التحميل...",
                Location = new Point(10, 50),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F)
            };

            dbSizeLabel = new Label
            {
                Text = "حجم قاعدة البيانات: جاري الحساب...",
                Location = new Point(10, 75),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F)
            };

            var versionLabel = new Label
            {
                Text = "إصدار البرنامج: 1.0.0 - العراقي",
                Location = new Point(10, 100),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69)
            };

            systemPanel.Controls.AddRange(new Control[] {
                titleLabel, dbLocationLabel, dbSizeLabel, versionLabel
            });
        }

        private void CreateBackupPanel()
        {
            backupPanel = new Panel
            {
                Size = new Size(980, 200),
                Location = new Point(10, 460),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "النسخ الاحتياطية وإدارة البيانات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            lastBackupLabel = new Label
            {
                Text = "آخر نسخة احتياطية: لم يتم إنشاء نسخة احتياطية بعد",
                Location = new Point(10, 50),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F)
            };

            backupButton = new Button
            {
                Text = "إنشاء نسخة احتياطية",
                Location = new Point(10, 80),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            backupButton.Click += BackupButton_Click;

            restoreButton = new Button
            {
                Text = "استعادة نسخة احتياطية",
                Location = new Point(180, 80),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            restoreButton.Click += RestoreButton_Click;

            clearDataButton = new Button
            {
                Text = "مسح جميع البيانات",
                Location = new Point(10, 130),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearDataButton.Click += ClearDataButton_Click;

            var warningLabel = new Label
            {
                Text = "⚠️ تحذير: مسح البيانات سيؤدي إلى فقدان جميع المعلومات نهائياً",
                Location = new Point(180, 140),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            backupPanel.Controls.AddRange(new Control[] {
                titleLabel, lastBackupLabel, backupButton, restoreButton,
                clearDataButton, warningLabel
            });
        }

        private void SaveStoreInfoButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(storeNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المحل", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    storeNameTextBox.Focus();
                    return;
                }

                var settings = DatabaseManager.GetStoreSettings() ?? new StoreSettings();
                
                settings.StoreName = storeNameTextBox.Text.Trim();
                settings.Phone = phoneTextBox.Text.Trim();
                settings.Address = addressTextBox.Text.Trim();
                settings.Email = emailTextBox.Text.Trim();
                settings.CurrencySymbol = currencySymbolTextBox.Text.Trim();

                // Handle logo
                if (logoPreviewPictureBox.Tag != null)
                {
                    var logoPath = logoPreviewPictureBox.Tag.ToString();
                    var logoDirectory = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                        "DubaiMobileStore", "logos");

                    if (!Directory.Exists(logoDirectory))
                        Directory.CreateDirectory(logoDirectory);

                    var logoFileName = $"logo_{DateTime.Now:yyyyMMdd_HHmmss}{Path.GetExtension(logoPath)}";
                    var savedLogoPath = Path.Combine(logoDirectory, logoFileName);

                    File.Copy(logoPath, savedLogoPath, true);
                    settings.LogoPath = savedLogoPath;
                }

                DatabaseManager.SaveStoreSettings(settings);

                MessageBox.Show("تم حفظ معلومات المحل بنجاح!", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المعلومات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BackupButton_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Database Files (*.db)|*.db|All Files (*.*)|*.*",
                    DefaultExt = "db",
                    FileName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var dbPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                        "DubaiMobileStore", "store.db");

                    File.Copy(dbPath, saveDialog.FileName, true);

                    MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح:\n{saveDialog.FileName}", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lastBackupLabel.Text = $"آخر نسخة احتياطية: {DateTime.Now:yyyy/MM/dd HH:mm}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RestoreButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "تحذير: استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية.\nهل أنت متأكد من المتابعة؟",
                    "تأكيد الاستعادة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result != DialogResult.Yes) return;

                var openDialog = new OpenFileDialog
                {
                    Filter = "Database Files (*.db)|*.db|All Files (*.*)|*.*",
                    Title = "اختر ملف النسخة الاحتياطية"
                };

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    var dbPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                        "DubaiMobileStore", "store.db");

                    File.Copy(openDialog.FileName, dbPath, true);

                    MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح!\nسيتم إعادة تشغيل البرنامج.", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    Application.Restart();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearDataButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "تحذير خطير: هذا الإجراء سيؤدي إلى مسح جميع البيانات نهائياً!\n\nسيتم مسح:\n- جميع المبيعات\n- جميع المشتريات\n- جميع المصروفات\n- جميع بيانات المخزون\n\nهل أنت متأكد تماماً من المتابعة؟",
                    "تأكيد مسح البيانات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Stop);

                if (result != DialogResult.Yes) return;

                // Second confirmation
                var secondResult = MessageBox.Show(
                    "تأكيد نهائي: اكتب 'نعم' للمتابعة",
                    "تأكيد نهائي",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Stop);

                if (secondResult != DialogResult.Yes) return;

                DatabaseManager.ClearAllData();

                MessageBox.Show("تم مسح جميع البيانات بنجاح!", "تم المسح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadSystemInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء مسح البيانات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadStoreSettings()
        {
            try
            {
                var settings = DatabaseManager.GetStoreSettings();
                if (settings != null)
                {
                    storeNameTextBox.Text = settings.StoreName;
                    phoneTextBox.Text = settings.Phone ?? "";
                    addressTextBox.Text = settings.Address ?? "";
                    emailTextBox.Text = settings.Email ?? "";
                    currencySymbolTextBox.Text = settings.CurrencySymbol ?? "د.إ";

                    // Load logo if exists
                    if (!string.IsNullOrEmpty(settings.LogoPath) && File.Exists(settings.LogoPath))
                    {
                        try
                        {
                            logoPreviewPictureBox.Image = Image.FromFile(settings.LogoPath);
                            logoPreviewPictureBox.Tag = settings.LogoPath;
                        }
                        catch
                        {
                            // Logo file not found or corrupted
                            logoPreviewPictureBox.Image = null;
                            logoPreviewPictureBox.Tag = null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الإعدادات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSystemInfo()
        {
            try
            {
                var dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "DubaiMobileStore", "store.db");

                dbLocationLabel.Text = $"موقع قاعدة البيانات: {dbPath}";

                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    var sizeKB = fileInfo.Length / 1024.0;
                    dbSizeLabel.Text = $"حجم قاعدة البيانات: {sizeKB:F1} كيلوبايت";
                }
                else
                {
                    dbSizeLabel.Text = "حجم قاعدة البيانات: غير موجود";
                }
            }
            catch (Exception)
            {
                dbLocationLabel.Text = "موقع قاعدة البيانات: خطأ في التحميل";
                dbSizeLabel.Text = "حجم قاعدة البيانات: خطأ في الحساب";
            }
        }
    }
}
