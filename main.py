#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج حسابات معرض دبي للموبايلات
الملف الرئيسي لتشغيل البرنامج

المطور: Augment Agent
التاريخ: 2024
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """التحقق من وجود المكتبات المطلوبة"""
    missing_packages = []
    
    try:
        import reportlab
    except ImportError:
        missing_packages.append("reportlab")
    
    try:
        import openpyxl
    except ImportError:
        missing_packages.append("openpyxl")
    
    try:
        from PIL import Image
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import arabic_reshaper
    except ImportError:
        missing_packages.append("arabic-reshaper")
    
    try:
        import bidi
    except ImportError:
        missing_packages.append("python-bidi")
    
    if missing_packages:
        error_msg = f"""
المكتبات التالية مفقودة ويجب تثبيتها:
{', '.join(missing_packages)}

لتثبيت المكتبات، قم بتشغيل الأمر التالي:
pip install {' '.join(missing_packages)}

أو استخدم:
pip install -r requirements.txt
        """
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showerror("مكتبات مفقودة", error_msg)
        root.destroy()
        return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['assets', 'reports', 'backups']
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"تم إنشاء مجلد: {directory}")
            except Exception as e:
                print(f"خطأ في إنشاء مجلد {directory}: {e}")

def main():
    """الوظيفة الرئيسية"""
    try:
        print("=" * 50)
        print("برنامج حسابات معرض دبي للموبايلات")
        print("=" * 50)
        
        # التحقق من المكتبات المطلوبة
        print("التحقق من المكتبات المطلوبة...")
        if not check_dependencies():
            print("فشل في التحقق من المكتبات المطلوبة")
            return
        
        print("✓ جميع المكتبات متوفرة")
        
        # إنشاء المجلدات المطلوبة
        print("إنشاء المجلدات المطلوبة...")
        create_directories()
        print("✓ تم إنشاء المجلدات")
        
        # تشغيل التطبيق
        print("تشغيل التطبيق...")
        from gui import MainApplication
        
        app = MainApplication()
        print("✓ تم تحميل التطبيق بنجاح")
        print("=" * 50)
        
        # تشغيل الواجهة الرسومية
        app.run()
        
    except ImportError as e:
        error_msg = f"""
خطأ في استيراد الوحدات:
{str(e)}

تأكد من أن جميع الملفات موجودة في نفس المجلد:
- main.py
- gui.py
- database.py
- models.py
- utils.py
- config.py
- requirements.txt
        """
        
        print(error_msg)
        
        # إظهار رسالة خطأ للمستخدم
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
            root.destroy()
        except:
            pass
    
    except Exception as e:
        error_msg = f"""
حدث خطأ غير متوقع:
{str(e)}

تفاصيل الخطأ:
{traceback.format_exc()}
        """
        
        print(error_msg)
        
        # إظهار رسالة خطأ للمستخدم
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", f"حدث خطأ غير متوقع:\n{str(e)}")
            root.destroy()
        except:
            pass

def show_help():
    """عرض معلومات المساعدة"""
    help_text = """
برنامج حسابات معرض دبي للموبايلات
=====================================

الاستخدام:
python main.py              - تشغيل البرنامج
python main.py --help       - عرض هذه المساعدة

المتطلبات:
- Python 3.7 أو أحدث
- المكتبات المذكورة في requirements.txt

التثبيت:
1. تأكد من تثبيت Python
2. قم بتشغيل: pip install -r requirements.txt
3. قم بتشغيل: python main.py

المميزات:
- إدارة المبيعات والمشتريات
- متابعة المخزون
- تسجيل المصروفات
- إنشاء التقارير والفواتير
- واجهة عربية سهلة الاستخدام

للدعم الفني:
تواصل مع فريق التطوير
    """
    print(help_text)

if __name__ == "__main__":
    # التحقق من معاملات سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['--version', '-v']:
            print("برنامج حسابات معرض دبي للموبايلات - الإصدار 1.0")
            sys.exit(0)
        else:
            print(f"معامل غير معروف: {sys.argv[1]}")
            print("استخدم --help لعرض المساعدة")
            sys.exit(1)
    
    # تشغيل البرنامج
    main()
