# دليل الاستخدام - برنامج حسابات معرض دبي للموبايلات

## 🚀 كيفية التشغيل

### الطريقة الأولى: تشغيل تلقائي
1. انقر نقراً مزدوجاً على ملف `START.bat`
2. سيتم تشغيل البرنامج تلقائياً

### الطريقة الثانية: تشغيل يدوي
```bash
# للواجهة الرسومية (إذا كانت متوفرة)
py main.py

# للوضع النصي المبسط
py run_simple.py

# لاختبار الوظائف الأساسية
py test_basic.py
```

## 📋 الوظائف المتاحة

### 1. إدارة المبيعات 💰
- إضافة عمليات بيع جديدة
- تسجيل معلومات العميل والجهاز
- حفظ أرقام IMEI
- إنشاء فواتير تلقائية
- تتبع البائعين

### 2. إدارة المشتريات 📦
- تسجيل عمليات الشراء من الموردين
- ربط المشتريات بالمخزون
- حساب هوامش الربح
- تتبع الموردين

### 3. إدارة المصروفات 💸
- تسجيل جميع أنواع المصروفات
- تصنيف المصروفات
- إضافة ملاحظات
- تتبع المصروفات الشهرية

### 4. المخزون 📋
- متابعة الأجهزة المتوفرة
- تتبع حالة الأجهزة (جديد/مستعمل)
- إدارة الكميات والأسعار

### 5. التقارير والإحصائيات 📊
- تقارير المبيعات اليومية والشهرية
- حساب الأرباح والخسائر
- تصدير البيانات (CSV/Text)
- إحصائيات مفصلة

### 6. الإعدادات ⚙️
- إعدادات المحل
- معلومات التواصل
- تخصيص الفواتير

## 🎯 كيفية الاستخدام

### إضافة عملية بيع جديدة
1. اختر "إضافة عملية بيع جديدة" من القائمة
2. أدخل اسم العميل (مطلوب)
3. أدخل رقم الهاتف (اختياري)
4. أدخل اسم الجهاز (مطلوب)
5. أدخل رقم IMEI (اختياري)
6. أدخل السعر (مطلوب)
7. أدخل اسم البائع (اختياري)
8. أدخل تاريخ البيع أو اتركه فارغاً لاستخدام اليوم
9. اختر إنشاء فاتورة إذا رغبت

### عرض الإحصائيات
1. اختر "عرض الإحصائيات" من القائمة
2. اختر الفترة الزمنية:
   - اليوم (1)
   - هذا الشهر (2)
   - فترة مخصصة (3)
3. ستظهر إحصائيات شاملة للمبيعات والمشتريات والأرباح

### إنشاء التقارير
1. اختر "إنشاء تقرير" من القائمة
2. اختر نوع التقرير:
   - تقرير مبيعات (1)
   - تقرير شامل (4)
3. حدد الفترة الزمنية
4. سيتم حفظ التقرير في مجلد `reports`

## 📁 هيكل الملفات

```
معرض_دبي_للموبايلات/
├── START.bat                 # ملف التشغيل التلقائي
├── main.py                   # البرنامج الرئيسي (واجهة رسومية)
├── run_simple.py             # الوضع النصي المبسط
├── test_basic.py             # اختبار الوظائف الأساسية
├── gui.py                    # واجهة المستخدم الرسومية
├── database.py               # إدارة قاعدة البيانات
├── models.py                 # نماذج البيانات
├── utils.py                  # الوظائف المساعدة
├── config.py                 # إعدادات البرنامج
├── requirements.txt          # المكتبات المطلوبة
├── README.md                 # ملف التوثيق الشامل
├── دليل_الاستخدام.md         # هذا الملف
├── dubai_mobile_store.db     # قاعدة البيانات (تُنشأ تلقائياً)
├── assets/                   # الصور والملفات المساعدة
├── reports/                  # التقارير المُنشأة
└── backups/                  # النسخ الاحتياطية
```

## 🔧 استكشاف الأخطاء

### مشكلة: "Python غير مثبت"
**الحل:**
1. قم بتحميل Python من https://python.org
2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
3. أعد تشغيل الكمبيوتر بعد التثبيت

### مشكلة: "Can't find a usable init.tcl"
**الحل:**
1. استخدم الوضع النصي بدلاً من الواجهة الرسومية
2. شغل `py run_simple.py` مباشرة
3. أو استخدم ملف `START.bat` الذي يتعامل مع هذه المشكلة تلقائياً

### مشكلة: "database is locked"
**الحل:**
1. تأكد من إغلاق جميع نسخ البرنامج
2. أعد تشغيل البرنامج
3. إذا استمرت المشكلة، احذف ملف `dubai_mobile_store.db` وأعد التشغيل

### مشكلة: "مكتبات مفقودة"
**الحل:**
البرنامج يعمل بدون مكتبات خارجية، لكن لتفعيل المميزات المتقدمة:
```bash
py -m pip install reportlab openpyxl Pillow arabic-reshaper python-bidi
```

## 💡 نصائح للاستخدام

### 1. النسخ الاحتياطية
- يتم حفظ البيانات في ملف `dubai_mobile_store.db`
- انسخ هذا الملف بانتظام كنسخة احتياطية
- يمكن نقل الملف لجهاز آخر لنقل البيانات

### 2. التقارير
- يتم حفظ جميع التقارير في مجلد `reports`
- التقارير النصية يمكن فتحها بأي محرر نصوص
- ملفات CSV يمكن فتحها بـ Excel

### 3. البحث
- يمكن البحث في المبيعات والمشتريات بالاسم أو رقم IMEI
- البحث غير حساس لحالة الأحرف
- يمكن ترك حقل البحث فارغاً لعرض جميع النتائج

### 4. التواريخ
- استخدم تنسيق YYYY-MM-DD للتواريخ (مثل: 2024-12-25)
- يمكن ترك حقل التاريخ فارغاً لاستخدام تاريخ اليوم
- في التقارير، يمكن ترك التواريخ فارغة لتشمل جميع الفترات

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من ملف README.md** للمعلومات التفصيلية
2. **جرب الوضع النصي** إذا لم تعمل الواجهة الرسومية
3. **استخدم ملف test_basic.py** لاختبار الوظائف الأساسية

## 🔄 التحديثات المستقبلية

### المميزات المخططة:
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] تقارير PDF متقدمة
- [ ] نظام المستخدمين والصلاحيات
- [ ] تنبيهات ذكية
- [ ] تحليلات متقدمة

---

**تم تطوير هذا البرنامج بواسطة Augment Agent**
*نظام ذكي لإدارة محلات الموبايلات*

📧 للدعم الفني: <EMAIL>
🌐 الموقع الإلكتروني: www.dubaimobile.com
