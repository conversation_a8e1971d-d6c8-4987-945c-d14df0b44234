using System;
using System.ComponentModel.DataAnnotations;

namespace DubaiMobileStore
{
    // نموذج بيانات المبيعات
    public class Sale
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "اسم العميل مطلوب")]
        public string CustomerName { get; set; } = string.Empty;
        
        public string? CustomerPhone { get; set; }
        
        [Required(ErrorMessage = "اسم الجهاز مطلوب")]
        public string DeviceName { get; set; } = string.Empty;
        
        public string? IMEI { get; set; }
        
        [Required(ErrorMessage = "السعر مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "السعر يجب أن يكون أكبر من صفر")]
        public decimal Price { get; set; }

        public string? Seller { get; set; }
        
        [Required]
        public DateTime SaleDate { get; set; } = DateTime.Now;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    // نموذج بيانات المشتريات
    public class Purchase
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "اسم المورد مطلوب")]
        public string SupplierName { get; set; } = string.Empty;
        
        public string? SupplierPhone { get; set; }
        
        [Required(ErrorMessage = "اسم الجهاز مطلوب")]
        public string DeviceName { get; set; } = string.Empty;
        
        public string? IMEI { get; set; }
        
        [Required(ErrorMessage = "السعر مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "السعر يجب أن يكون أكبر من صفر")]
        public decimal Price { get; set; }
        
        [Required]
        public DateTime PurchaseDate { get; set; } = DateTime.Now;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    // نموذج بيانات المصروفات
    public class Expense
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "نوع المصروف مطلوب")]
        public string ExpenseType { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }
        
        [Required]
        public DateTime ExpenseDate { get; set; } = DateTime.Now;
        
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    // نموذج بيانات المخزون
    public class Inventory
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "اسم الجهاز مطلوب")]
        public string DeviceName { get; set; } = string.Empty;
        
        public string? IMEI { get; set; }
        
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public int Quantity { get; set; } = 1;
        
        [Required(ErrorMessage = "سعر الشراء مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من صفر")]
        public decimal PurchasePrice { get; set; }
        
        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من صفر")]
        public decimal SellingPrice { get; set; }
        
        [Required]
        public string Condition { get; set; } = "جديد"; // جديد أو مستعمل

        [Required]
        public string Status { get; set; } = "متوفر"; // متوفر أو مباع

        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    // نموذج إعدادات المحل
    public class StoreSettings
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المحل مطلوب")]
        public string StoreName { get; set; } = "معرض دبي للموبايلات";

        public string? Phone { get; set; }

        public string? Address { get; set; }

        public string? Email { get; set; }

        public string? LogoPath { get; set; }

        public string Currency { get; set; } = "AED"; // UAE Dirham

        public string CurrencySymbol { get; set; } = "د.إ"; // درهم إماراتي

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    // نموذج الإحصائيات
    public class Statistics
    {
        public int SalesCount { get; set; }
        public decimal SalesTotal { get; set; }
        public int PurchasesCount { get; set; }
        public decimal PurchasesTotal { get; set; }
        public int ExpensesCount { get; set; }
        public decimal ExpensesTotal { get; set; }
        public int ReturnsCount { get; set; }
        public decimal ReturnsTotal { get; set; }
        public decimal NetProfit => SalesTotal - PurchasesTotal - ExpensesTotal - ReturnsTotal;
    }

    // فئات المصروفات الشائعة
    public static class ExpenseTypes
    {
        public static readonly string[] CommonTypes = {
            "إيجار المحل",
            "فواتير الكهرباء",
            "فواتير الماء",
            "فواتير الهاتف",
            "فواتير الإنترنت",
            "رواتب الموظفين",
            "صيانة وإصلاح",
            "مواد تنظيف",
            "قرطاسية ومكتبية",
            "مواصلات",
            "وقود",
            "إعلانات وتسويق",
            "ضرائب ورسوم",
            "تأمين",
            "أخرى"
        };
    }

    // حالات الأجهزة
    public static class DeviceConditions
    {
        public static readonly string[] Conditions = { "جديد", "مستعمل", "مُجدد" };
    }

    // حالات المخزون
    public static class InventoryStatus
    {
        public static readonly string[] StatusList = { "متوفر", "مباع", "معطل", "قيد الصيانة" };
    }

    // نموذج بيانات المرتجعات
    public class Return
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم العميل مطلوب")]
        public string CustomerName { get; set; } = string.Empty;

        public string? CustomerPhone { get; set; }

        [Required(ErrorMessage = "اسم الجهاز مطلوب")]
        public string DeviceName { get; set; } = string.Empty;

        public string? IMEI { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "سبب الإرجاع مطلوب")]
        public string ReturnReason { get; set; } = string.Empty;

        public string? Notes { get; set; }

        public int? OriginalSaleId { get; set; } // ربط بالبيع الأصلي

        [Required]
        public DateTime ReturnDate { get; set; } = DateTime.Now;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    // نموذج ملخص المخزون
    public class InventorySummary
    {
        public int TotalItems { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockItems { get; set; }
        public int AvailableItems { get; set; }
        public int SoldItems { get; set; }
    }

    // أسباب الإرجاع الشائعة
    public static class ReturnReasons
    {
        public static readonly string[] CommonReasons = {
            "عيب في الجهاز",
            "عدم رضا العميل",
            "جهاز لا يعمل",
            "مشكلة في الشاشة",
            "مشكلة في البطارية",
            "مشكلة في الشحن",
            "مشكلة في الصوت",
            "مشكلة في الكاميرا",
            "تغيير رأي العميل",
            "جهاز مختلف عن المطلوب",
            "سبب آخر"
        };
    }
}
