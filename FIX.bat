@echo off
setlocal enabledelayedexpansion
title Auto Fix - Dubai Mobile Store

echo ==========================================
echo AUTO FIX UTILITY
echo ==========================================
echo.

echo This utility will attempt to fix common issues
echo.
pause

echo [STEP 1] Creating missing directories...
if not exist "reports" (
    mkdir reports
    echo   Created: reports directory
) else (
    echo   OK: reports directory exists
)

if not exist "assets" (
    mkdir assets
    echo   Created: assets directory
) else (
    echo   OK: assets directory exists
)

if not exist "backups" (
    mkdir backups
    echo   Created: backups directory
) else (
    echo   OK: backups directory exists
)
echo.

echo [STEP 2] Checking file permissions...
echo Testing file write permissions...
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    del test_write.tmp
    echo   OK: Write permissions working
) else (
    echo   WARNING: Write permission issues detected
    echo   Try running as Administrator
)
echo.

echo [STEP 3] Creating simplified launcher...
echo Creating SIMPLE_RUN.bat...

echo @echo off > SIMPLE_RUN.bat
echo title Dubai Mobile Store >> SIMPLE_RUN.bat
echo echo Starting Dubai Mobile Store... >> SIMPLE_RUN.bat
echo echo. >> SIMPLE_RUN.bat
echo. >> SIMPLE_RUN.bat
echo REM Try different Python commands >> SIMPLE_RUN.bat
echo py run_simple.py >> SIMPLE_RUN.bat
echo if %%errorlevel%% neq 0 ( >> SIMPLE_RUN.bat
echo     python run_simple.py >> SIMPLE_RUN.bat
echo ) >> SIMPLE_RUN.bat
echo if %%errorlevel%% neq 0 ( >> SIMPLE_RUN.bat
echo     python3 run_simple.py >> SIMPLE_RUN.bat
echo ) >> SIMPLE_RUN.bat
echo. >> SIMPLE_RUN.bat
echo pause >> SIMPLE_RUN.bat

echo   Created: SIMPLE_RUN.bat
echo.

echo [STEP 4] Creating Python test script...
echo Creating test_system.py...

echo # Test script for Dubai Mobile Store > test_system.py
echo import sys >> test_system.py
echo import os >> test_system.py
echo. >> test_system.py
echo print("=== SYSTEM TEST ===") >> test_system.py
echo print(f"Python version: {sys.version}") >> test_system.py
echo print(f"Current directory: {os.getcwd()}") >> test_system.py
echo. >> test_system.py
echo # Test imports >> test_system.py
echo try: >> test_system.py
echo     import sqlite3 >> test_system.py
echo     print("✓ SQLite3 available") >> test_system.py
echo except ImportError: >> test_system.py
echo     print("✗ SQLite3 not available") >> test_system.py
echo. >> test_system.py
echo try: >> test_system.py
echo     import tkinter >> test_system.py
echo     print("✓ Tkinter available") >> test_system.py
echo except ImportError: >> test_system.py
echo     print("✗ Tkinter not available") >> test_system.py
echo. >> test_system.py
echo # Test required files >> test_system.py
echo required_files = ['run_simple.py', 'database.py', 'models.py'] >> test_system.py
echo for file in required_files: >> test_system.py
echo     if os.path.exists(file): >> test_system.py
echo         print(f"✓ {file} found") >> test_system.py
echo     else: >> test_system.py
echo         print(f"✗ {file} missing") >> test_system.py
echo. >> test_system.py
echo print("=== TEST COMPLETE ===") >> test_system.py
echo input("Press Enter to continue...") >> test_system.py

echo   Created: test_system.py
echo.

echo [STEP 5] Testing the fix...
echo Running system test...
echo.

py test_system.py 2>nul
if !errorlevel! neq 0 (
    python test_system.py 2>nul
    if !errorlevel! neq 0 (
        echo WARNING: Could not run Python test
        echo Please check Python installation
    )
)

echo.
echo [FIX COMPLETE]
echo ==========================================
echo.
echo Available launchers:
echo 1. LAUNCH.bat     - Advanced launcher with diagnostics
echo 2. SIMPLE_RUN.bat - Simple launcher
echo 3. RUN.bat        - Original launcher
echo.
echo Try running one of these files to start the application
echo.
pause
