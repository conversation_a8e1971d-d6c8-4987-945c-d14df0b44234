# برنامج حسابات معرض دبي للموبايلات

## نظام إدارة شامل لمحلات الموبايلات

برنامج حسابات متكامل مصمم خصيصاً لإدارة محلات الموبايلات، يوفر جميع الأدوات اللازمة لتتبع المبيعات والمشتريات والمخزون والمصروفات.

## 🌟 المميزات الرئيسية

### 📱 إدارة المبيعات
- تسجيل عمليات البيع مع تفاصيل العميل والجهاز
- حفظ معلومات IMEI لكل جهاز
- تتبع البائعين والعمولات
- إنشاء فواتير PDF احترافية

### 📦 إدارة المشتريات
- تسجيل عمليات الشراء من الموردين
- ربط المشتريات بالمخزون تلقائياً
- حساب هوامش الربح

### 💸 إدارة المصروفات
- تسجيل جميع أنواع المصروفات
- تصنيف المصروفات حسب النوع
- تتبع المصروفات الشهرية والسنوية

### 📋 إدارة المخزون
- متابعة الأجهزة المتوفرة والمباعة
- تتبع حالة الأجهزة (جديد/مستعمل)
- تنبيهات عند نفاد المخزون

### 📊 التقارير والإحصائيات
- تقارير مفصلة للمبيعات والمشتريات
- حساب الأرباح والخسائر
- تصدير التقارير بصيغة PDF و Excel
- إحصائيات يومية وشهرية وسنوية

### ⚙️ الإعدادات والتخصيص
- إعدادات المحل والشعار
- تخصيص معلومات الفواتير
- نسخ احتياطية للبيانات

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

### خطوات التثبيت

#### الطريقة الأولى: التثبيت التلقائي (مُوصى بها)
1. **تحميل المشروع** وفك الضغط
2. **تشغيل الإعداد التلقائي**
   ```bash
   py setup.py
   ```
3. **تشغيل البرنامج**
   - انقر نقراً مزدوجاً على `START.bat`
   - أو شغل: `py main.py`

#### الطريقة الثانية: التثبيت اليدوي
1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd dubai-mobile-store
   ```

2. **تثبيت المكتبات الاختيارية** (للمميزات المتقدمة)
   ```bash
   py -m pip install reportlab openpyxl Pillow arabic-reshaper python-bidi
   ```
   أو شغل: `install_optional.bat`

3. **تشغيل البرنامج**
   ```bash
   # للواجهة الرسومية
   py main.py

   # للوضع النصي المبسط
   py run_simple.py

   # لاختبار الوظائف
   py test_basic.py
   ```

### المكتبات المطلوبة
- `reportlab` - لإنشاء ملفات PDF
- `openpyxl` - لملفات Excel
- `Pillow` - لمعالجة الصور
- `arabic-reshaper` - لدعم النصوص العربية
- `python-bidi` - لاتجاه النص العربي

## 📁 هيكل المشروع

```
dubai-mobile-store/
├── main.py              # الملف الرئيسي
├── gui.py               # واجهة المستخدم الرسومية
├── database.py          # إدارة قاعدة البيانات
├── models.py            # نماذج البيانات
├── utils.py             # الوظائف المساعدة
├── config.py            # إعدادات البرنامج
├── requirements.txt     # قائمة المكتبات
├── README.md           # ملف التوثيق
├── assets/             # الصور والملفات المساعدة
├── reports/            # التقارير المُنشأة
└── backups/            # النسخ الاحتياطية
```

## 🎯 كيفية الاستخدام

### البدء السريع

1. **تشغيل البرنامج**
   - قم بتشغيل `python main.py`
   - ستظهر الواجهة الرئيسية للبرنامج

2. **إعداد معلومات المحل**
   - اذهب إلى قسم "الإعدادات"
   - أدخل اسم المحل ومعلومات التواصل
   - ارفع شعار المحل (اختياري)

3. **إضافة أول عملية بيع**
   - اذهب إلى قسم "المبيعات"
   - املأ بيانات العميل والجهاز
   - اضغط "إضافة البيع"

### الوظائف المتقدمة

#### إنشاء التقارير
1. اذهب إلى قسم "التقارير"
2. اختر نوع التقرير (مبيعات، مشتريات، أرباح)
3. حدد الفترة الزمنية
4. اختر تصدير PDF أو Excel

#### إدارة المخزون
1. اذهب إلى قسم "المخزون"
2. أضف الأجهزة الجديدة
3. تتبع الكميات والأسعار
4. راقب حالة الأجهزة

## 🔧 التخصيص والإعدادات

### تخصيص الألوان
يمكن تعديل ألوان الواجهة من ملف `config.py`:

```python
COLORS = {
    'primary': '#2E86AB',      # اللون الأساسي
    'secondary': '#A23B72',    # اللون الثانوي
    'success': '#F18F01',      # لون النجاح
    'danger': '#C73E1D',       # لون الخطر
    # ...
}
```

### إعدادات قاعدة البيانات
يمكن تغيير اسم قاعدة البيانات من `config.py`:

```python
DATABASE_NAME = "dubai_mobile_store.db"
```

## 🛡️ الأمان والنسخ الاحتياطية

- يتم حفظ البيانات في قاعدة بيانات SQLite محلية
- إمكانية إنشاء نسخ احتياطية تلقائية
- تشفير البيانات الحساسة (قيد التطوير)

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ: "مكتبات مفقودة"**
```bash
pip install -r requirements.txt
```

**خطأ: "لا يمكن فتح قاعدة البيانات"**
- تأكد من وجود صلاحيات الكتابة في مجلد البرنامج
- قم بتشغيل البرنامج كمدير (Windows)

**مشكلة في عرض النصوص العربية**
- تأكد من تثبيت `arabic-reshaper` و `python-bidi`
- تحقق من وجود خطوط عربية في النظام

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966 XX XXX XXXX
- 🌐 الموقع الإلكتروني: www.dubaimobile.com

## 📄 الترخيص

هذا البرنامج مطور خصيصاً لمعرض دبي للموبايلات.
جميع الحقوق محفوظة © 2024

## 🔄 التحديثات المستقبلية

### الإصدار القادم (v1.1)
- [ ] دعم قواعد بيانات متعددة
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] تقارير متقدمة بالذكاء الاصطناعي

### طلبات المميزات
إذا كان لديك اقتراحات لمميزات جديدة، يرجى التواصل معنا.

---

**تم تطوير هذا البرنامج بواسطة Augment Agent**
*نظام ذكي لإدارة محلات الموبايلات*
