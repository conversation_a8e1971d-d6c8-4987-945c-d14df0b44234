using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class ExpensesUserControl : UserControl
    {
        private Panel formPanel;
        private Panel listPanel;
        private ComboBox expenseTypeComboBox;
        private TextBox customExpenseTypeTextBox;
        private TextBox amountTextBox;
        private DateTimePicker expenseDatePicker;
        private TextBox notesTextBox;
        private Button addButton;
        private Button clearButton;
        private DataGridView expensesDataGridView;
        private TextBox searchTextBox;
        private Label totalExpensesLabel;

        public ExpensesUserControl()
        {
            InitializeComponent();
            LoadExpenses();
            UpdateTotalExpenses();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFormPanel();
            CreateListPanel();

            this.Controls.AddRange(new Control[] { formPanel, listPanel });
        }

        private void CreateFormPanel()
        {
            formPanel = new Panel
            {
                Size = new Size(980, 280),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إضافة مصروف جديد",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Row 1
            var expenseTypeLabel = new Label
            {
                Text = "نوع المصروف:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            expenseTypeComboBox = new ComboBox
            {
                Location = new Point(120, 47),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            expenseTypeComboBox.Items.AddRange(ExpenseTypes.CommonTypes);
            expenseTypeComboBox.SelectedIndexChanged += ExpenseTypeComboBox_SelectedIndexChanged;

            var customTypeLabel = new Label
            {
                Text = "نوع آخر:",
                Location = new Point(350, 50),
                AutoSize = true
            };

            customExpenseTypeTextBox = new TextBox
            {
                Location = new Point(420, 47),
                Size = new Size(200, 23),
                Enabled = false
            };

            // Row 2
            var amountLabel = new Label
            {
                Text = "المبلغ (د.ع):",
                Location = new Point(10, 90),
                AutoSize = true
            };

            amountTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(150, 23)
            };

            var expenseDateLabel = new Label
            {
                Text = "تاريخ المصروف:",
                Location = new Point(300, 90),
                AutoSize = true
            };

            expenseDatePicker = new DateTimePicker
            {
                Location = new Point(420, 87),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Row 3
            var notesLabel = new Label
            {
                Text = "ملاحظات:",
                Location = new Point(10, 130),
                AutoSize = true
            };

            notesTextBox = new TextBox
            {
                Location = new Point(120, 127),
                Size = new Size(450, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Buttons
            addButton = new Button
            {
                Text = "إضافة المصروف",
                Location = new Point(120, 210),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addButton.Click += AddButton_Click;

            clearButton = new Button
            {
                Text = "مسح الحقول",
                Location = new Point(260, 210),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            formPanel.Controls.AddRange(new Control[] {
                titleLabel, expenseTypeLabel, expenseTypeComboBox,
                customTypeLabel, customExpenseTypeTextBox, amountLabel,
                amountTextBox, expenseDateLabel, expenseDatePicker,
                notesLabel, notesTextBox, addButton, clearButton
            });
        }

        private void CreateListPanel()
        {
            listPanel = new Panel
            {
                Size = new Size(980, 390),
                Location = new Point(10, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "قائمة المصروفات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            totalExpensesLabel = new Label
            {
                Text = "إجمالي المصروفات: 0 د.ع",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(600, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 45),
                AutoSize = true
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 42),
                Size = new Size(300, 23)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            expensesDataGridView = new DataGridView
            {
                Location = new Point(10, 75),
                Size = new Size(950, 300),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            listPanel.Controls.AddRange(new Control[] {
                titleLabel, totalExpensesLabel, searchLabel, searchTextBox, expensesDataGridView
            });
        }

        private void ExpenseTypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool isOtherSelected = expenseTypeComboBox.SelectedItem?.ToString() == "أخرى";
            customExpenseTypeTextBox.Enabled = isOtherSelected;
            if (isOtherSelected)
            {
                customExpenseTypeTextBox.Focus();
            }
            else
            {
                customExpenseTypeTextBox.Clear();
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                string expenseType = "";
                if (expenseTypeComboBox.SelectedItem?.ToString() == "أخرى")
                {
                    if (string.IsNullOrWhiteSpace(customExpenseTypeTextBox.Text))
                    {
                        MessageBox.Show("يرجى إدخال نوع المصروف", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        customExpenseTypeTextBox.Focus();
                        return;
                    }
                    expenseType = customExpenseTypeTextBox.Text.Trim();
                }
                else if (expenseTypeComboBox.SelectedItem != null)
                {
                    expenseType = expenseTypeComboBox.SelectedItem.ToString();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار نوع المصروف", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    expenseTypeComboBox.Focus();
                    return;
                }

                if (!decimal.TryParse(amountTextBox.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    amountTextBox.Focus();
                    return;
                }

                // Create expense object
                var expense = new Expense
                {
                    ExpenseType = expenseType,
                    Amount = amount,
                    ExpenseDate = expenseDatePicker.Value.Date,
                    Notes = string.IsNullOrWhiteSpace(notesTextBox.Text) ? 
                        null : notesTextBox.Text.Trim()
                };

                // Save to database
                var expenseId = DatabaseManager.AddExpense(expense);

                MessageBox.Show($"تم إضافة المصروف بنجاح!\nرقم العملية: {expenseId}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear form and refresh list
                ClearForm();
                LoadExpenses();
                UpdateTotalExpenses();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المصروف:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            expenseTypeComboBox.SelectedIndex = -1;
            customExpenseTypeTextBox.Clear();
            customExpenseTypeTextBox.Enabled = false;
            amountTextBox.Clear();
            notesTextBox.Clear();
            expenseDatePicker.Value = DateTime.Now;
            expenseTypeComboBox.Focus();
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            LoadExpenses(searchTextBox.Text);
        }

        private void LoadExpenses(string search = null)
        {
            try
            {
                var expenses = DatabaseManager.GetExpenses(50, search);
                expensesDataGridView.DataSource = expenses;

                // Configure columns
                if (expensesDataGridView.Columns.Count > 0)
                {
                    expensesDataGridView.Columns["Id"].HeaderText = "الرقم";
                    expensesDataGridView.Columns["ExpenseType"].HeaderText = "نوع المصروف";
                    expensesDataGridView.Columns["Amount"].HeaderText = "المبلغ (د.ع)";
                    expensesDataGridView.Columns["ExpenseDate"].HeaderText = "التاريخ";
                    expensesDataGridView.Columns["Notes"].HeaderText = "ملاحظات";
                    
                    // Hide CreatedAt column
                    expensesDataGridView.Columns["CreatedAt"].Visible = false;

                    // Format amount and date columns
                    expensesDataGridView.Columns["Amount"].DefaultCellStyle.Format = "N0";
                    expensesDataGridView.Columns["ExpenseDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المصروفات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateTotalExpenses()
        {
            try
            {
                var totalExpenses = DatabaseManager.GetTotalExpenses();
                totalExpensesLabel.Text = $"إجمالي المصروفات: {totalExpenses:N0} د.ع";
            }
            catch (Exception)
            {
                totalExpensesLabel.Text = "خطأ في حساب المجموع";
            }
        }
    }
}
