# -*- coding: utf-8 -*-
"""
نماذج البيانات للبرنامج
"""

from datetime import datetime
from dataclasses import dataclass
from typing import Optional

@dataclass
class Sale:
    """نموذج بيانات المبيعات"""
    id: Optional[int] = None
    customer_name: str = ""
    customer_phone: str = ""
    device_name: str = ""
    imei: str = ""
    price: float = 0.0
    seller: str = ""
    sale_date: str = ""
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@dataclass
class Purchase:
    """نموذج بيانات المشتريات"""
    id: Optional[int] = None
    supplier_name: str = ""
    supplier_phone: str = ""
    device_name: str = ""
    imei: str = ""
    price: float = 0.0
    purchase_date: str = ""
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@dataclass
class Expense:
    """نموذج بيانات المصروفات"""
    id: Optional[int] = None
    expense_type: str = ""
    amount: float = 0.0
    expense_date: str = ""
    notes: str = ""
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@dataclass
class Inventory:
    """نموذج بيانات المخزون"""
    id: Optional[int] = None
    device_name: str = ""
    imei: str = ""
    quantity: int = 0
    purchase_price: float = 0.0
    selling_price: float = 0.0
    condition: str = "جديد"  # جديد أو مستعمل
    status: str = "متوفر"   # متوفر أو مباع
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@dataclass
class StoreSettings:
    """نموذج إعدادات المحل"""
    id: Optional[int] = None
    store_name: str = ""
    phone: str = ""
    address: str = ""
    email: str = ""
    logo_path: str = ""
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.updated_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

class ValidationError(Exception):
    """خطأ في التحقق من صحة البيانات"""
    pass

def validate_phone(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف"""
    if not phone:
        return False
    # إزالة المسافات والرموز
    phone = phone.replace(" ", "").replace("-", "").replace("(", "").replace(")", "")
    # التحقق من أن الرقم يحتوي على أرقام فقط وطوله مناسب
    return phone.isdigit() and 10 <= len(phone) <= 15

def validate_imei(imei: str) -> bool:
    """التحقق من صحة رقم IMEI"""
    if not imei:
        return False
    # إزالة المسافات
    imei = imei.replace(" ", "")
    # التحقق من أن IMEI يحتوي على 15 رقم
    return imei.isdigit() and len(imei) == 15

def validate_price(price: str) -> bool:
    """التحقق من صحة السعر"""
    try:
        float_price = float(price)
        return float_price >= 0
    except ValueError:
        return False
