#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من برنامج حسابات معرض دبي للموبايلات
تعمل بدون أي مكتبات خارجية
"""

import os
import sqlite3
from datetime import datetime, date

def create_database():
    """إنشاء قاعدة البيانات"""
    conn = sqlite3.connect('simple_store.db')
    cursor = conn.cursor()
    
    # جدول المبيعات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            customer_phone TEXT,
            device_name TEXT NOT NULL,
            price REAL NOT NULL,
            sale_date TEXT NOT NULL,
            created_at TEXT NOT NULL
        )
    ''')
    
    # جدول المشتريات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_name TEXT NOT NULL,
            device_name TEXT NOT NULL,
            price REAL NOT NULL,
            purchase_date TEXT NOT NULL,
            created_at TEXT NOT NULL
        )
    ''')
    
    # جدول المصروفات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            expense_type TEXT NOT NULL,
            amount REAL NOT NULL,
            expense_date TEXT NOT NULL,
            notes TEXT,
            created_at TEXT NOT NULL
        )
    ''')
    
    conn.commit()
    conn.close()

def add_sale():
    """إضافة عملية بيع"""
    print("\n" + "="*50)
    print("إضافة عملية بيع جديدة")
    print("="*50)
    
    customer_name = input("اسم العميل: ").strip()
    if not customer_name:
        print("خطأ: اسم العميل مطلوب")
        return
    
    customer_phone = input("رقم الهاتف (اختياري): ").strip()
    device_name = input("اسم الجهاز: ").strip()
    if not device_name:
        print("خطأ: اسم الجهاز مطلوب")
        return
    
    try:
        price = float(input("السعر: ").strip())
        if price <= 0:
            print("خطأ: السعر يجب أن يكون أكبر من صفر")
            return
    except ValueError:
        print("خطأ: السعر غير صحيح")
        return
    
    sale_date = input(f"تاريخ البيع (اتركه فارغ لاستخدام اليوم {date.today()}): ").strip()
    if not sale_date:
        sale_date = date.today().strftime("%Y-%m-%d")
    
    # حفظ في قاعدة البيانات
    conn = sqlite3.connect('simple_store.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO sales (customer_name, customer_phone, device_name, price, sale_date, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (customer_name, customer_phone, device_name, price, sale_date, 
          datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    
    sale_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    print(f"\n✅ تم إضافة عملية البيع بنجاح! رقم الفاتورة: {sale_id}")
    
    # إنشاء فاتورة بسيطة
    create_simple_invoice(sale_id, customer_name, device_name, price, sale_date)

def create_simple_invoice(sale_id, customer_name, device_name, price, sale_date):
    """إنشاء فاتورة بسيطة"""
    if not os.path.exists('reports'):
        os.makedirs('reports')
    
    filename = f"reports/invoice_{sale_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    invoice_content = f"""
{'='*50}
معرض دبي للموبايلات
{'='*50}

فاتورة بيع رقم: {sale_id}
التاريخ: {sale_date}

معلومات العميل:
الاسم: {customer_name}

تفاصيل المنتج:
الجهاز: {device_name}
السعر: {price:.2f} ريال سعودي

{'='*50}
المجموع الإجمالي: {price:.2f} ريال سعودي
{'='*50}

شكراً لتعاملكم معنا
تاريخ الطباعة: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(invoice_content)
    
    print(f"📄 تم إنشاء الفاتورة: {filename}")

def show_sales():
    """عرض المبيعات"""
    print("\n" + "="*50)
    print("قائمة المبيعات")
    print("="*50)
    
    conn = sqlite3.connect('simple_store.db')
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM sales ORDER BY created_at DESC LIMIT 10")
    sales = cursor.fetchall()
    
    if not sales:
        print("لا توجد مبيعات")
        conn.close()
        return
    
    for sale in sales:
        print(f"رقم: {sale[0]} | العميل: {sale[1]} | الجهاز: {sale[3]}")
        print(f"السعر: {sale[4]:.2f} ريال | التاريخ: {sale[5]}")
        print("-" * 50)
    
    conn.close()

def show_statistics():
    """عرض الإحصائيات"""
    print("\n" + "="*50)
    print("الإحصائيات")
    print("="*50)
    
    conn = sqlite3.connect('simple_store.db')
    cursor = conn.cursor()
    
    # إحصائيات اليوم
    today = date.today().strftime("%Y-%m-%d")
    
    cursor.execute("SELECT COUNT(*), SUM(price) FROM sales WHERE sale_date = ?", (today,))
    sales_today = cursor.fetchone()
    
    cursor.execute("SELECT COUNT(*), SUM(price) FROM purchases WHERE purchase_date = ?", (today,))
    purchases_today = cursor.fetchone()
    
    cursor.execute("SELECT COUNT(*), SUM(amount) FROM expenses WHERE expense_date = ?", (today,))
    expenses_today = cursor.fetchone()
    
    sales_count = sales_today[0] or 0
    sales_total = sales_today[1] or 0.0
    purchases_total = purchases_today[1] or 0.0
    expenses_total = expenses_today[1] or 0.0
    
    profit = sales_total - purchases_total - expenses_total
    
    print(f"إحصائيات اليوم ({today}):")
    print(f"المبيعات: {sales_count} عملية بقيمة {sales_total:.2f} ريال")
    print(f"المشتريات: {purchases_total:.2f} ريال")
    print(f"المصروفات: {expenses_total:.2f} ريال")
    print(f"صافي الربح: {profit:.2f} ريال")
    
    conn.close()

def main_menu():
    """القائمة الرئيسية"""
    create_database()
    
    while True:
        print("\n" + "="*60)
        print("🏪 برنامج حسابات معرض دبي للموبايلات - النسخة المبسطة")
        print("="*60)
        print("1. إضافة عملية بيع")
        print("2. عرض المبيعات")
        print("3. عرض الإحصائيات")
        print("4. خروج")
        print("="*60)
        
        choice = input("اختر رقم العملية: ").strip()
        
        try:
            if choice == "1":
                add_sale()
            elif choice == "2":
                show_sales()
            elif choice == "3":
                show_statistics()
            elif choice == "4":
                print("شكراً لاستخدام البرنامج!")
                break
            else:
                print("اختيار غير صحيح")
        
        except KeyboardInterrupt:
            print("\nتم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"حدث خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    print("🚀 تشغيل النسخة المبسطة من برنامج معرض دبي للموبايلات")
    print("هذه النسخة تعمل بدون أي مكتبات خارجية")
    print()
    
    try:
        main_menu()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
