using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class SalesUserControl : UserControl
    {
        private Panel formPanel;
        private Panel listPanel;
        private TextBox customerNameTextBox;
        private TextBox customerPhoneTextBox;
        private TextBox deviceNameTextBox;
        private TextBox imeiTextBox;
        private TextBox priceTextBox;
        private TextBox sellerTextBox;
        private DateTimePicker saleDatePicker;
        private Button addButton;
        private Button clearButton;
        private DataGridView salesDataGridView;
        private TextBox searchTextBox;
        private Button printInvoiceButton;
        private Button reprintInvoiceButton;
        private Button advancedSearchButton;

        public SalesUserControl()
        {
            InitializeComponent();
            LoadSales();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFormPanel();
            CreateListPanel();

            this.Controls.AddRange(new Control[] { formPanel, listPanel });
        }

        private void CreateFormPanel()
        {
            formPanel = new Panel
            {
                Size = new Size(980, 250),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إضافة عملية بيع جديدة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Row 1
            var customerNameLabel = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            customerNameTextBox = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(200, 23)
            };

            var customerPhoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(350, 50),
                AutoSize = true
            };

            customerPhoneTextBox = new TextBox
            {
                Location = new Point(450, 47),
                Size = new Size(150, 23)
            };

            // Row 2
            var deviceNameLabel = new Label
            {
                Text = "اسم الجهاز:",
                Location = new Point(10, 90),
                AutoSize = true
            };

            deviceNameTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(200, 23)
            };

            var imeiLabel = new Label
            {
                Text = "رقم IMEI:",
                Location = new Point(350, 90),
                AutoSize = true
            };

            imeiTextBox = new TextBox
            {
                Location = new Point(450, 87),
                Size = new Size(200, 23)
            };

            // Row 3
            var priceLabel = new Label
            {
                Text = "السعر:",
                Location = new Point(10, 130),
                AutoSize = true
            };

            priceTextBox = new TextBox
            {
                Location = new Point(120, 127),
                Size = new Size(150, 23)
            };

            var sellerLabel = new Label
            {
                Text = "البائع:",
                Location = new Point(300, 130),
                AutoSize = true
            };

            sellerTextBox = new TextBox
            {
                Location = new Point(370, 127),
                Size = new Size(150, 23)
            };

            var saleDateLabel = new Label
            {
                Text = "تاريخ البيع:",
                Location = new Point(550, 130),
                AutoSize = true
            };

            saleDatePicker = new DateTimePicker
            {
                Location = new Point(650, 127),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Buttons
            addButton = new Button
            {
                Text = "إضافة البيع",
                Location = new Point(120, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addButton.Click += AddButton_Click;

            clearButton = new Button
            {
                Text = "مسح الحقول",
                Location = new Point(240, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            formPanel.Controls.AddRange(new Control[] {
                titleLabel, customerNameLabel, customerNameTextBox,
                customerPhoneLabel, customerPhoneTextBox, deviceNameLabel,
                deviceNameTextBox, imeiLabel, imeiTextBox, priceLabel,
                priceTextBox, sellerLabel, sellerTextBox, saleDateLabel,
                saleDatePicker, addButton, clearButton
            });
        }

        private void CreateListPanel()
        {
            listPanel = new Panel
            {
                Size = new Size(980, 420),
                Location = new Point(10, 270),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "قائمة المبيعات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            var searchLabel = new Label
            {
                Text = "البحث (الاسم، IMEI، الهاتف):",
                Location = new Point(10, 45),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(220, 42),
                Size = new Size(350, 23),
                PlaceholderText = "ابحث بالاسم أو IMEI أو رقم الهاتف..."
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;
            searchTextBox.KeyDown += SearchTextBox_KeyDown;

            // Advanced search button
            advancedSearchButton = new Button
            {
                Text = "🔍 بحث متقدم",
                Location = new Point(580, 40),
                Size = new Size(100, 27),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            advancedSearchButton.Click += AdvancedSearchButton_Click;

            // Print buttons
            printInvoiceButton = new Button
            {
                Text = "🖨️ طباعة الفاتورة",
                Location = new Point(690, 40),
                Size = new Size(120, 27),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Enabled = false
            };
            printInvoiceButton.Click += PrintInvoiceButton_Click;

            reprintInvoiceButton = new Button
            {
                Text = "📄 إعادة طباعة",
                Location = new Point(820, 40),
                Size = new Size(120, 27),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Enabled = false
            };
            reprintInvoiceButton.Click += ReprintInvoiceButton_Click;

            salesDataGridView = new DataGridView
            {
                Location = new Point(10, 75),
                Size = new Size(950, 330),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            salesDataGridView.SelectionChanged += SalesDataGridView_SelectionChanged;

            listPanel.Controls.AddRange(new Control[] {
                titleLabel, searchLabel, searchTextBox, advancedSearchButton,
                printInvoiceButton, reprintInvoiceButton, salesDataGridView
            });
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    customerNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(deviceNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    deviceNameTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(priceTextBox.Text, out decimal price) || price <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    priceTextBox.Focus();
                    return;
                }

                // Create sale object
                var sale = new Sale
                {
                    CustomerName = customerNameTextBox.Text.Trim(),
                    CustomerPhone = string.IsNullOrWhiteSpace(customerPhoneTextBox.Text) ? 
                        null : customerPhoneTextBox.Text.Trim(),
                    DeviceName = deviceNameTextBox.Text.Trim(),
                    IMEI = string.IsNullOrWhiteSpace(imeiTextBox.Text) ? 
                        null : imeiTextBox.Text.Trim(),
                    Price = price,
                    Seller = string.IsNullOrWhiteSpace(sellerTextBox.Text) ? 
                        null : sellerTextBox.Text.Trim(),
                    SaleDate = saleDatePicker.Value.Date
                };

                // Save to database
                var saleId = DatabaseManager.AddSale(sale);

                MessageBox.Show($"تم إضافة البيع بنجاح!\nرقم الفاتورة: {saleId}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Ask about invoice with three options
                var invoiceDialog = new Form
                {
                    Text = "خيارات الفاتورة",
                    Size = new Size(400, 200),
                    StartPosition = FormStartPosition.CenterParent,
                    RightToLeft = RightToLeft.Yes,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                var label = new Label
                {
                    Text = "اختر نوع الفاتورة:",
                    Location = new Point(20, 20),
                    AutoSize = true,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                };

                var printButton = new Button
                {
                    Text = "طباعة فورية",
                    Location = new Point(20, 60),
                    Size = new Size(100, 35),
                    BackColor = Color.FromArgb(40, 167, 69),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                var previewButton = new Button
                {
                    Text = "معاينة وطباعة",
                    Location = new Point(140, 60),
                    Size = new Size(100, 35),
                    BackColor = Color.FromArgb(23, 162, 184),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                var textButton = new Button
                {
                    Text = "فاتورة نصية",
                    Location = new Point(260, 60),
                    Size = new Size(100, 35),
                    BackColor = Color.FromArgb(255, 193, 7),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                var cancelButton = new Button
                {
                    Text = "إلغاء",
                    Location = new Point(160, 110),
                    Size = new Size(80, 30),
                    BackColor = Color.FromArgb(108, 117, 125),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                DialogResult dialogResult = DialogResult.Cancel;

                printButton.Click += (s, e) => { dialogResult = DialogResult.Yes; invoiceDialog.Close(); };
                previewButton.Click += (s, e) => { dialogResult = DialogResult.Retry; invoiceDialog.Close(); };
                textButton.Click += (s, e) => { dialogResult = DialogResult.No; invoiceDialog.Close(); };
                cancelButton.Click += (s, e) => { dialogResult = DialogResult.Cancel; invoiceDialog.Close(); };

                invoiceDialog.Controls.AddRange(new Control[] { label, printButton, previewButton, textButton, cancelButton });

                var result = invoiceDialog.ShowDialog();

                if (result != DialogResult.Cancel)
                {
                    GenerateInvoice(sale, saleId, dialogResult);
                }

                // Clear form and refresh list
                ClearForm();
                LoadSales();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة البيع:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            customerNameTextBox.Clear();
            customerPhoneTextBox.Clear();
            deviceNameTextBox.Clear();
            imeiTextBox.Clear();
            priceTextBox.Clear();
            sellerTextBox.Clear();
            saleDatePicker.Value = DateTime.Now;
            customerNameTextBox.Focus();
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            LoadSales(searchTextBox.Text);
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true;
                PerformAdvancedSearch();
            }
        }

        private void PerformAdvancedSearch()
        {
            string searchText = searchTextBox.Text.Trim();
            if (string.IsNullOrEmpty(searchText))
            {
                LoadSales();
                return;
            }

            // Check if search text looks like IMEI (15 digits)
            if (searchText.Length == 15 && searchText.All(char.IsDigit))
            {
                var sales = DatabaseManager.GetSales(null, searchText);
                if (sales.Count == 0)
                {
                    MessageBox.Show($"لم يتم العثور على أي مبيعات بـ IMEI: {searchText}", "نتائج البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"تم العثور على {sales.Count} عملية بيع بـ IMEI: {searchText}", "نتائج البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }

            LoadSales(searchText);
        }

        private void AdvancedSearchButton_Click(object sender, EventArgs e)
        {
            ShowAdvancedSearchDialog();
        }

        private void ShowAdvancedSearchDialog()
        {
            var searchDialog = new Form
            {
                Text = "البحث المتقدم",
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var titleLabel = new Label
            {
                Text = "البحث المتقدم في المبيعات",
                Location = new Point(20, 20),
                AutoSize = true,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Customer name search
            var customerLabel = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(20, 60),
                AutoSize = true
            };

            var customerTextBox = new TextBox
            {
                Location = new Point(120, 57),
                Size = new Size(200, 23)
            };

            // IMEI search
            var imeiLabel = new Label
            {
                Text = "رقم IMEI:",
                Location = new Point(20, 100),
                AutoSize = true
            };

            var imeiTextBox = new TextBox
            {
                Location = new Point(120, 97),
                Size = new Size(200, 23),
                PlaceholderText = "أدخل 15 رقم"
            };

            // Phone search
            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(20, 140),
                AutoSize = true
            };

            var phoneTextBox = new TextBox
            {
                Location = new Point(120, 137),
                Size = new Size(200, 23)
            };

            // Device name search
            var deviceLabel = new Label
            {
                Text = "اسم الجهاز:",
                Location = new Point(20, 180),
                AutoSize = true
            };

            var deviceTextBox = new TextBox
            {
                Location = new Point(120, 177),
                Size = new Size(200, 23)
            };

            // Date range
            var dateLabel = new Label
            {
                Text = "فترة زمنية:",
                Location = new Point(20, 220),
                AutoSize = true
            };

            var startDatePicker = new DateTimePicker
            {
                Location = new Point(120, 217),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddDays(-30)
            };

            var toLabel = new Label
            {
                Text = "إلى",
                Location = new Point(250, 220),
                AutoSize = true
            };

            var endDatePicker = new DateTimePicker
            {
                Location = new Point(280, 217),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Buttons
            var searchButton = new Button
            {
                Text = "🔍 بحث",
                Location = new Point(120, 270),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            var clearButton = new Button
            {
                Text = "مسح",
                Location = new Point(220, 270),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(320, 270),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            searchButton.Click += (s, e) => {
                PerformAdvancedSearchWithCriteria(
                    customerTextBox.Text.Trim(),
                    imeiTextBox.Text.Trim(),
                    phoneTextBox.Text.Trim(),
                    deviceTextBox.Text.Trim(),
                    startDatePicker.Value.Date,
                    endDatePicker.Value.Date
                );
                searchDialog.Close();
            };

            clearButton.Click += (s, e) => {
                customerTextBox.Clear();
                imeiTextBox.Clear();
                phoneTextBox.Clear();
                deviceTextBox.Clear();
                startDatePicker.Value = DateTime.Now.AddDays(-30);
                endDatePicker.Value = DateTime.Now;
            };

            cancelButton.Click += (s, e) => searchDialog.Close();

            searchDialog.Controls.AddRange(new Control[] {
                titleLabel, customerLabel, customerTextBox, imeiLabel, imeiTextBox,
                phoneLabel, phoneTextBox, deviceLabel, deviceTextBox,
                dateLabel, startDatePicker, toLabel, endDatePicker,
                searchButton, clearButton, cancelButton
            });

            searchDialog.ShowDialog();
        }

        private void PerformAdvancedSearchWithCriteria(string customer, string imei, string phone, string device, DateTime startDate, DateTime endDate)
        {
            try
            {
                var sales = DatabaseManager.GetAdvancedSales(customer, imei, phone, device, startDate, endDate);
                salesDataGridView.DataSource = sales;

                string searchSummary = $"نتائج البحث: {sales.Count} عملية بيع";
                if (!string.IsNullOrEmpty(imei))
                    searchSummary += $" | IMEI: {imei}";
                if (!string.IsNullOrEmpty(customer))
                    searchSummary += $" | العميل: {customer}";

                MessageBox.Show(searchSummary, "نتائج البحث المتقدم",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Configure columns
                ConfigureSalesColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ConfigureSalesColumns()
        {
            if (salesDataGridView.Columns.Count > 0)
            {
                salesDataGridView.Columns["Id"].HeaderText = "الرقم";
                salesDataGridView.Columns["CustomerName"].HeaderText = "العميل";
                salesDataGridView.Columns["CustomerPhone"].HeaderText = "الهاتف";
                salesDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                salesDataGridView.Columns["IMEI"].HeaderText = "IMEI";
                salesDataGridView.Columns["Price"].HeaderText = "السعر (د.إ)";
                salesDataGridView.Columns["Seller"].HeaderText = "البائع";
                salesDataGridView.Columns["SaleDate"].HeaderText = "التاريخ";

                // Hide CreatedAt column
                salesDataGridView.Columns["CreatedAt"].Visible = false;

                // Format price column for UAE Dirham
                salesDataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
                salesDataGridView.Columns["SaleDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
        }

        private void SalesDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = salesDataGridView.SelectedRows.Count > 0;
            printInvoiceButton.Enabled = hasSelection;
            reprintInvoiceButton.Enabled = hasSelection;
        }

        private void PrintInvoiceButton_Click(object sender, EventArgs e)
        {
            if (salesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عملية بيع لطباعة فاتورتها", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedRow = salesDataGridView.SelectedRows[0];
                var sale = CreateSaleFromRow(selectedRow);

                // Show print options dialog
                ShowPrintOptionsDialog(sale);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ReprintInvoiceButton_Click(object sender, EventArgs e)
        {
            if (salesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عملية بيع لإعادة طباعة فاتورتها", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedRow = salesDataGridView.SelectedRows[0];
                var sale = CreateSaleFromRow(selectedRow);

                // Direct reprint with preview
                var storeSettings = DatabaseManager.GetStoreSettings();
                if (storeSettings != null)
                {
                    using (var invoiceGenerator = new InvoiceGenerator())
                    {
                        invoiceGenerator.GenerateInvoice(sale, storeSettings);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إعادة طباعة الفاتورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSales(string search = null)
        {
            try
            {
                var sales = DatabaseManager.GetSales(50, search);
                salesDataGridView.DataSource = sales;

                // Configure columns
                if (salesDataGridView.Columns.Count > 0)
                {
                    salesDataGridView.Columns["Id"].HeaderText = "الرقم";
                    salesDataGridView.Columns["CustomerName"].HeaderText = "العميل";
                    salesDataGridView.Columns["CustomerPhone"].HeaderText = "الهاتف";
                    salesDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                    salesDataGridView.Columns["IMEI"].HeaderText = "IMEI";
                    salesDataGridView.Columns["Price"].HeaderText = "السعر";
                    salesDataGridView.Columns["Seller"].HeaderText = "البائع";
                    salesDataGridView.Columns["SaleDate"].HeaderText = "التاريخ";
                    
                    // Hide CreatedAt column
                    salesDataGridView.Columns["CreatedAt"].Visible = false;

                    // Format price column for Iraqi Dinar
                    salesDataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
                    salesDataGridView.Columns["SaleDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

                    // Add currency symbol to price column header
                    salesDataGridView.Columns["Price"].HeaderText = "السعر (د.ع)";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المبيعات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateInvoice(Sale sale, int saleId, DialogResult invoiceType)
        {
            try
            {
                var storeSettings = DatabaseManager.GetStoreSettings();
                if (storeSettings == null)
                {
                    MessageBox.Show("لم يتم العثور على إعدادات المحل", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                sale.Id = saleId; // Set the ID for the invoice

                switch (invoiceType)
                {
                    case DialogResult.Yes: // Direct print
                        using (var invoiceGenerator = new InvoiceGenerator())
                        {
                            invoiceGenerator.PrintInvoiceDirectly(sale, storeSettings);
                        }
                        break;

                    case DialogResult.Retry: // Preview and print
                        using (var invoiceGenerator = new InvoiceGenerator())
                        {
                            invoiceGenerator.GenerateInvoice(sale, storeSettings);
                        }
                        break;

                    case DialogResult.No: // Text invoice
                        GenerateTextInvoice(sale, saleId, storeSettings);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء الفاتورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateTextInvoice(Sale sale, int saleId, StoreSettings storeSettings)
        {
            var invoiceContent = $@"
{'='*60}
{storeSettings.StoreName}
{'='*60}

الهاتف: {storeSettings.Phone ?? "غير محدد"}
العنوان: {storeSettings.Address ?? "غير محدد"}
البريد الإلكتروني: {storeSettings.Email ?? "غير محدد"}

{'='*60}
فاتورة بيع
{'='*60}

رقم الفاتورة: {saleId}
تاريخ البيع: {sale.SaleDate:yyyy/MM/dd}
وقت الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}
البائع: {sale.Seller ?? "غير محدد"}

{'='*60}
معلومات العميل
{'='*60}

الاسم: {sale.CustomerName}
رقم الهاتف: {sale.CustomerPhone ?? "غير محدد"}

{'='*60}
تفاصيل المنتج
{'='*60}

اسم الجهاز: {sale.DeviceName}
رقم IMEI: {sale.IMEI ?? "غير محدد"}
الكمية: 1
السعر: {sale.Price:N0} {storeSettings.CurrencySymbol}

{'='*60}
المجموع الإجمالي: {sale.Price:N0} {storeSettings.CurrencySymbol}
{'='*60}

شكراً لتعاملكم معنا
نتطلع لخدمتكم مرة أخرى

تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}
";

            // Save invoice to file
            var fileName = $"Invoice_{saleId}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            var filePath = System.IO.Path.Combine(Environment.GetFolderPath(
                Environment.SpecialFolder.Desktop), fileName);

            System.IO.File.WriteAllText(filePath, invoiceContent, System.Text.Encoding.UTF8);

            MessageBox.Show($"تم إنشاء الفاتورة بنجاح:\n{filePath}", "نجح",
                MessageBoxButtons.OK, MessageBoxIcon.Information);

            // Open the file
            System.Diagnostics.Process.Start("notepad.exe", filePath);
        }

        private Sale CreateSaleFromRow(DataGridViewRow row)
        {
            return new Sale
            {
                Id = Convert.ToInt32(row.Cells["Id"].Value),
                CustomerName = row.Cells["CustomerName"].Value?.ToString() ?? "",
                CustomerPhone = row.Cells["CustomerPhone"].Value?.ToString(),
                DeviceName = row.Cells["DeviceName"].Value?.ToString() ?? "",
                IMEI = row.Cells["IMEI"].Value?.ToString(),
                Price = Convert.ToDecimal(row.Cells["Price"].Value),
                Seller = row.Cells["Seller"].Value?.ToString(),
                SaleDate = Convert.ToDateTime(row.Cells["SaleDate"].Value),
                CreatedAt = Convert.ToDateTime(row.Cells["CreatedAt"].Value)
            };
        }

        private void ShowPrintOptionsDialog(Sale sale)
        {
            var printDialog = new Form
            {
                Text = "خيارات طباعة الفاتورة",
                Size = new Size(450, 250),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var titleLabel = new Label
            {
                Text = $"طباعة فاتورة رقم: {sale.Id}",
                Location = new Point(20, 20),
                AutoSize = true,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            var customerLabel = new Label
            {
                Text = $"العميل: {sale.CustomerName} | الجهاز: {sale.DeviceName}",
                Location = new Point(20, 50),
                AutoSize = true,
                Font = new Font("Segoe UI", 9F)
            };

            var directPrintButton = new Button
            {
                Text = "🖨️ طباعة مباشرة",
                Location = new Point(20, 90),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            var previewPrintButton = new Button
            {
                Text = "👁️ معاينة وطباعة",
                Location = new Point(160, 90),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            var textInvoiceButton = new Button
            {
                Text = "📄 فاتورة نصية",
                Location = new Point(300, 90),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(180, 150),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            directPrintButton.Click += (s, e) => {
                printDialog.Close();
                PrintSaleInvoice(sale, DialogResult.Yes);
            };

            previewPrintButton.Click += (s, e) => {
                printDialog.Close();
                PrintSaleInvoice(sale, DialogResult.Retry);
            };

            textInvoiceButton.Click += (s, e) => {
                printDialog.Close();
                PrintSaleInvoice(sale, DialogResult.No);
            };

            cancelButton.Click += (s, e) => printDialog.Close();

            printDialog.Controls.AddRange(new Control[] {
                titleLabel, customerLabel, directPrintButton, previewPrintButton,
                textInvoiceButton, cancelButton
            });

            printDialog.ShowDialog();
        }

        private void PrintSaleInvoice(Sale sale, DialogResult printType)
        {
            try
            {
                var storeSettings = DatabaseManager.GetStoreSettings();
                if (storeSettings == null)
                {
                    MessageBox.Show("لم يتم العثور على إعدادات المحل", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                switch (printType)
                {
                    case DialogResult.Yes: // Direct print
                        using (var invoiceGenerator = new InvoiceGenerator())
                        {
                            invoiceGenerator.PrintInvoiceDirectly(sale, storeSettings);
                        }
                        break;

                    case DialogResult.Retry: // Preview and print
                        using (var invoiceGenerator = new InvoiceGenerator())
                        {
                            invoiceGenerator.GenerateInvoice(sale, storeSettings);
                        }
                        break;

                    case DialogResult.No: // Text invoice
                        GenerateTextInvoice(sale, sale.Id, storeSettings);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
