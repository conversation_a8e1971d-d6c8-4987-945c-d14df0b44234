using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class SalesUserControl : UserControl
    {
        private Panel formPanel;
        private Panel listPanel;
        private TextBox customerNameTextBox;
        private TextBox customerPhoneTextBox;
        private TextBox deviceNameTextBox;
        private TextBox imeiTextBox;
        private TextBox priceTextBox;
        private TextBox sellerTextBox;
        private DateTimePicker saleDatePicker;
        private Button addButton;
        private Button clearButton;
        private DataGridView salesDataGridView;
        private TextBox searchTextBox;

        public SalesUserControl()
        {
            InitializeComponent();
            LoadSales();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFormPanel();
            CreateListPanel();

            this.Controls.AddRange(new Control[] { formPanel, listPanel });
        }

        private void CreateFormPanel()
        {
            formPanel = new Panel
            {
                Size = new Size(980, 250),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إضافة عملية بيع جديدة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Row 1
            var customerNameLabel = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            customerNameTextBox = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(200, 23)
            };

            var customerPhoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(350, 50),
                AutoSize = true
            };

            customerPhoneTextBox = new TextBox
            {
                Location = new Point(450, 47),
                Size = new Size(150, 23)
            };

            // Row 2
            var deviceNameLabel = new Label
            {
                Text = "اسم الجهاز:",
                Location = new Point(10, 90),
                AutoSize = true
            };

            deviceNameTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(200, 23)
            };

            var imeiLabel = new Label
            {
                Text = "رقم IMEI:",
                Location = new Point(350, 90),
                AutoSize = true
            };

            imeiTextBox = new TextBox
            {
                Location = new Point(450, 87),
                Size = new Size(200, 23)
            };

            // Row 3
            var priceLabel = new Label
            {
                Text = "السعر:",
                Location = new Point(10, 130),
                AutoSize = true
            };

            priceTextBox = new TextBox
            {
                Location = new Point(120, 127),
                Size = new Size(150, 23)
            };

            var sellerLabel = new Label
            {
                Text = "البائع:",
                Location = new Point(300, 130),
                AutoSize = true
            };

            sellerTextBox = new TextBox
            {
                Location = new Point(370, 127),
                Size = new Size(150, 23)
            };

            var saleDateLabel = new Label
            {
                Text = "تاريخ البيع:",
                Location = new Point(550, 130),
                AutoSize = true
            };

            saleDatePicker = new DateTimePicker
            {
                Location = new Point(650, 127),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Buttons
            addButton = new Button
            {
                Text = "إضافة البيع",
                Location = new Point(120, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addButton.Click += AddButton_Click;

            clearButton = new Button
            {
                Text = "مسح الحقول",
                Location = new Point(240, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            formPanel.Controls.AddRange(new Control[] {
                titleLabel, customerNameLabel, customerNameTextBox,
                customerPhoneLabel, customerPhoneTextBox, deviceNameLabel,
                deviceNameTextBox, imeiLabel, imeiTextBox, priceLabel,
                priceTextBox, sellerLabel, sellerTextBox, saleDateLabel,
                saleDatePicker, addButton, clearButton
            });
        }

        private void CreateListPanel()
        {
            listPanel = new Panel
            {
                Size = new Size(980, 420),
                Location = new Point(10, 270),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "قائمة المبيعات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 45),
                AutoSize = true
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 42),
                Size = new Size(300, 23)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            salesDataGridView = new DataGridView
            {
                Location = new Point(10, 75),
                Size = new Size(950, 330),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            listPanel.Controls.AddRange(new Control[] {
                titleLabel, searchLabel, searchTextBox, salesDataGridView
            });
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    customerNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(deviceNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    deviceNameTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(priceTextBox.Text, out decimal price) || price <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    priceTextBox.Focus();
                    return;
                }

                // Create sale object
                var sale = new Sale
                {
                    CustomerName = customerNameTextBox.Text.Trim(),
                    CustomerPhone = string.IsNullOrWhiteSpace(customerPhoneTextBox.Text) ? 
                        null : customerPhoneTextBox.Text.Trim(),
                    DeviceName = deviceNameTextBox.Text.Trim(),
                    IMEI = string.IsNullOrWhiteSpace(imeiTextBox.Text) ? 
                        null : imeiTextBox.Text.Trim(),
                    Price = price,
                    Seller = string.IsNullOrWhiteSpace(sellerTextBox.Text) ? 
                        null : sellerTextBox.Text.Trim(),
                    SaleDate = saleDatePicker.Value.Date
                };

                // Save to database
                var saleId = DatabaseManager.AddSale(sale);

                MessageBox.Show($"تم إضافة البيع بنجاح!\nرقم الفاتورة: {saleId}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Ask about invoice
                var result = MessageBox.Show("هل تريد إنشاء فاتورة للبيع؟", "إنشاء فاتورة", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    GenerateInvoice(sale, saleId);
                }

                // Clear form and refresh list
                ClearForm();
                LoadSales();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة البيع:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            customerNameTextBox.Clear();
            customerPhoneTextBox.Clear();
            deviceNameTextBox.Clear();
            imeiTextBox.Clear();
            priceTextBox.Clear();
            sellerTextBox.Clear();
            saleDatePicker.Value = DateTime.Now;
            customerNameTextBox.Focus();
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            LoadSales(searchTextBox.Text);
        }

        private void LoadSales(string search = null)
        {
            try
            {
                var sales = DatabaseManager.GetSales(50, search);
                salesDataGridView.DataSource = sales;

                // Configure columns
                if (salesDataGridView.Columns.Count > 0)
                {
                    salesDataGridView.Columns["Id"].HeaderText = "الرقم";
                    salesDataGridView.Columns["CustomerName"].HeaderText = "العميل";
                    salesDataGridView.Columns["CustomerPhone"].HeaderText = "الهاتف";
                    salesDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                    salesDataGridView.Columns["IMEI"].HeaderText = "IMEI";
                    salesDataGridView.Columns["Price"].HeaderText = "السعر";
                    salesDataGridView.Columns["Seller"].HeaderText = "البائع";
                    salesDataGridView.Columns["SaleDate"].HeaderText = "التاريخ";
                    
                    // Hide CreatedAt column
                    salesDataGridView.Columns["CreatedAt"].Visible = false;

                    // Format price column for Iraqi Dinar
                    salesDataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
                    salesDataGridView.Columns["SaleDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

                    // Add currency symbol to price column header
                    salesDataGridView.Columns["Price"].HeaderText = "السعر (د.ع)";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المبيعات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateInvoice(Sale sale, int saleId)
        {
            try
            {
                var storeSettings = DatabaseManager.GetStoreSettings();
                if (storeSettings == null)
                {
                    MessageBox.Show("لم يتم العثور على إعدادات المحل", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Show invoice options
                var result = MessageBox.Show(
                    "اختر نوع الفاتورة:\n\nنعم = فاتورة مطبوعة (تصميم احترافي)\nلا = فاتورة نصية (ملف نصي)\nإلغاء = عدم إنشاء فاتورة",
                    "نوع الفاتورة",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Cancel)
                    return;

                if (result == DialogResult.Yes)
                {
                    // Generate professional printable invoice
                    using (var invoiceGenerator = new InvoiceGenerator())
                    {
                        sale.Id = saleId; // Set the ID for the invoice
                        invoiceGenerator.GenerateInvoice(sale, storeSettings);
                    }
                }
                else
                {
                    // Generate text invoice
                    GenerateTextInvoice(sale, saleId, storeSettings);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء الفاتورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateTextInvoice(Sale sale, int saleId, StoreSettings storeSettings)
        {
            var invoiceContent = $@"
{'='*60}
{storeSettings.StoreName}
{'='*60}

الهاتف: {storeSettings.Phone ?? "غير محدد"}
العنوان: {storeSettings.Address ?? "غير محدد"}
البريد الإلكتروني: {storeSettings.Email ?? "غير محدد"}

{'='*60}
فاتورة بيع
{'='*60}

رقم الفاتورة: {saleId}
تاريخ البيع: {sale.SaleDate:yyyy/MM/dd}
وقت الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}
البائع: {sale.Seller ?? "غير محدد"}

{'='*60}
معلومات العميل
{'='*60}

الاسم: {sale.CustomerName}
رقم الهاتف: {sale.CustomerPhone ?? "غير محدد"}

{'='*60}
تفاصيل المنتج
{'='*60}

اسم الجهاز: {sale.DeviceName}
رقم IMEI: {sale.IMEI ?? "غير محدد"}
الكمية: 1
السعر: {sale.Price:N0} {storeSettings.CurrencySymbol}

{'='*60}
المجموع الإجمالي: {sale.Price:N0} {storeSettings.CurrencySymbol}
{'='*60}

شكراً لتعاملكم معنا
نتطلع لخدمتكم مرة أخرى

تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}
";

            // Save invoice to file
            var fileName = $"Invoice_{saleId}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            var filePath = System.IO.Path.Combine(Environment.GetFolderPath(
                Environment.SpecialFolder.Desktop), fileName);

            System.IO.File.WriteAllText(filePath, invoiceContent, System.Text.Encoding.UTF8);

            MessageBox.Show($"تم إنشاء الفاتورة بنجاح:\n{filePath}", "نجح",
                MessageBoxButtons.OK, MessageBoxIcon.Information);

            // Open the file
            System.Diagnostics.Process.Start("notepad.exe", filePath);
        }
    }
}
