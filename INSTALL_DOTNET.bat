@echo off
title Install .NET SDK for Dubai Mobile Store

echo ==========================================
echo .NET SDK Installation Helper
echo ==========================================
echo.

echo This script will help you install .NET 6.0 SDK
echo which is required to run Dubai Mobile Store application
echo.

REM Check if .NET is already installed
echo Checking current .NET installation...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo .NET SDK is already installed:
    dotnet --version
    echo.
    echo You can now run BUILD.bat to build the application
    pause
    exit /b 0
)

echo .NET SDK not found
echo.

echo Opening .NET download page...
echo.
echo Please download and install .NET 6.0 SDK from the page that will open
echo.
echo Installation steps:
echo 1. Download .NET 6.0 SDK (not Runtime)
echo 2. Run the installer
echo 3. Restart your computer
echo 4. Run BUILD.bat to build the application
echo.

pause

REM Open .NET download page
start "" "https://dotnet.microsoft.com/download/dotnet/6.0"

echo.
echo After installing .NET SDK:
echo 1. Restart your computer
echo 2. Run BUILD.bat to build the application
echo 3. Run RUN_CSHARP.bat to start the application
echo.
pause
