using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class InventoryUserControl : UserControl
    {
        private Panel formPanel;
        private Panel listPanel;
        private TextBox deviceNameTextBox;
        private TextBox imeiTextBox;
        private TextBox quantityTextBox;
        private TextBox purchasePriceTextBox;
        private TextBox sellingPriceTextBox;
        private ComboBox conditionComboBox;
        private ComboBox statusComboBox;
        private Button addButton;
        private Button updateButton;
        private Button clearButton;
        private DataGridView inventoryDataGridView;
        private TextBox searchTextBox;
        private Label totalItemsLabel;
        private Label totalValueLabel;
        private Label lowStockLabel;

        public InventoryUserControl()
        {
            InitializeComponent();
            LoadInventory();
            UpdateSummary();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFormPanel();
            CreateListPanel();

            this.Controls.AddRange(new Control[] { formPanel, listPanel });
        }

        private void CreateFormPanel()
        {
            formPanel = new Panel
            {
                Size = new Size(980, 280),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إدارة المخزون",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Row 1
            var deviceNameLabel = new Label
            {
                Text = "اسم الجهاز:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            deviceNameTextBox = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(200, 23)
            };

            var imeiLabel = new Label
            {
                Text = "رقم IMEI:",
                Location = new Point(350, 50),
                AutoSize = true
            };

            imeiTextBox = new TextBox
            {
                Location = new Point(450, 47),
                Size = new Size(200, 23)
            };

            // Row 2
            var quantityLabel = new Label
            {
                Text = "الكمية:",
                Location = new Point(10, 90),
                AutoSize = true
            };

            quantityTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(100, 23),
                Text = "1"
            };

            var purchasePriceLabel = new Label
            {
                Text = "سعر الشراء (د.ع):",
                Location = new Point(250, 90),
                AutoSize = true
            };

            purchasePriceTextBox = new TextBox
            {
                Location = new Point(370, 87),
                Size = new Size(120, 23)
            };

            var sellingPriceLabel = new Label
            {
                Text = "سعر البيع (د.ع):",
                Location = new Point(520, 90),
                AutoSize = true
            };

            sellingPriceTextBox = new TextBox
            {
                Location = new Point(630, 87),
                Size = new Size(120, 23)
            };

            // Row 3
            var conditionLabel = new Label
            {
                Text = "حالة الجهاز:",
                Location = new Point(10, 130),
                AutoSize = true
            };

            conditionComboBox = new ComboBox
            {
                Location = new Point(120, 127),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            conditionComboBox.Items.AddRange(DeviceConditions.Conditions);
            conditionComboBox.SelectedIndex = 0; // Default to "جديد"

            var statusLabel = new Label
            {
                Text = "حالة المخزون:",
                Location = new Point(270, 130),
                AutoSize = true
            };

            statusComboBox = new ComboBox
            {
                Location = new Point(380, 127),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusComboBox.Items.AddRange(InventoryStatus.StatusList);
            statusComboBox.SelectedIndex = 0; // Default to "متوفر"

            // Buttons
            addButton = new Button
            {
                Text = "إضافة للمخزون",
                Location = new Point(120, 180),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addButton.Click += AddButton_Click;

            updateButton = new Button
            {
                Text = "تحديث العنصر",
                Location = new Point(260, 180),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Enabled = false
            };
            updateButton.Click += UpdateButton_Click;

            clearButton = new Button
            {
                Text = "مسح الحقول",
                Location = new Point(400, 180),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            formPanel.Controls.AddRange(new Control[] {
                titleLabel, deviceNameLabel, deviceNameTextBox,
                imeiLabel, imeiTextBox, quantityLabel, quantityTextBox,
                purchasePriceLabel, purchasePriceTextBox, sellingPriceLabel,
                sellingPriceTextBox, conditionLabel, conditionComboBox,
                statusLabel, statusComboBox, addButton, updateButton, clearButton
            });
        }

        private void CreateListPanel()
        {
            listPanel = new Panel
            {
                Size = new Size(980, 390),
                Location = new Point(10, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "قائمة المخزون",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Summary labels
            totalItemsLabel = new Label
            {
                Text = "إجمالي الأصناف: 0",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(400, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(40, 167, 69)
            };

            totalValueLabel = new Label
            {
                Text = "قيمة المخزون: 0 د.ع",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(550, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(23, 162, 184)
            };

            lowStockLabel = new Label
            {
                Text = "نفاد المخزون: 0",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(700, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 45),
                AutoSize = true
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 42),
                Size = new Size(300, 23)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            inventoryDataGridView = new DataGridView
            {
                Location = new Point(10, 75),
                Size = new Size(950, 300),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            inventoryDataGridView.CellClick += InventoryDataGridView_CellClick;

            listPanel.Controls.AddRange(new Control[] {
                titleLabel, totalItemsLabel, totalValueLabel, lowStockLabel,
                searchLabel, searchTextBox, inventoryDataGridView
            });
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                var inventory = CreateInventoryFromForm();
                var inventoryId = DatabaseManager.AddInventory(inventory);

                MessageBox.Show($"تم إضافة العنصر للمخزون بنجاح!\nرقم العنصر: {inventoryId}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                LoadInventory();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة العنصر:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;
                if (inventoryDataGridView.SelectedRows.Count == 0) return;

                var selectedRow = inventoryDataGridView.SelectedRows[0];
                var inventoryId = (int)selectedRow.Cells["Id"].Value;

                var inventory = CreateInventoryFromForm();
                inventory.Id = inventoryId;

                DatabaseManager.UpdateInventory(inventory);

                MessageBox.Show("تم تحديث العنصر بنجاح!", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                LoadInventory();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث العنصر:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            LoadInventory(searchTextBox.Text);
        }

        private void InventoryDataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var row = inventoryDataGridView.Rows[e.RowIndex];
                LoadInventoryToForm(row);
                updateButton.Enabled = true;
                addButton.Text = "إضافة جديد";
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(deviceNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                deviceNameTextBox.Focus();
                return false;
            }

            if (!int.TryParse(quantityTextBox.Text, out int quantity) || quantity <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                quantityTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(purchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                purchasePriceTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(sellingPriceTextBox.Text, out decimal sellingPrice) || sellingPrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                sellingPriceTextBox.Focus();
                return false;
            }

            return true;
        }

        private Inventory CreateInventoryFromForm()
        {
            return new Inventory
            {
                DeviceName = deviceNameTextBox.Text.Trim(),
                IMEI = string.IsNullOrWhiteSpace(imeiTextBox.Text) ? null : imeiTextBox.Text.Trim(),
                Quantity = int.Parse(quantityTextBox.Text),
                PurchasePrice = decimal.Parse(purchasePriceTextBox.Text),
                SellingPrice = decimal.Parse(sellingPriceTextBox.Text),
                Condition = conditionComboBox.SelectedItem?.ToString() ?? "جديد",
                Status = statusComboBox.SelectedItem?.ToString() ?? "متوفر"
            };
        }

        private void LoadInventoryToForm(DataGridViewRow row)
        {
            deviceNameTextBox.Text = row.Cells["DeviceName"].Value?.ToString() ?? "";
            imeiTextBox.Text = row.Cells["IMEI"].Value?.ToString() ?? "";
            quantityTextBox.Text = row.Cells["Quantity"].Value?.ToString() ?? "1";
            purchasePriceTextBox.Text = row.Cells["PurchasePrice"].Value?.ToString() ?? "";
            sellingPriceTextBox.Text = row.Cells["SellingPrice"].Value?.ToString() ?? "";
            
            var condition = row.Cells["Condition"].Value?.ToString() ?? "جديد";
            var status = row.Cells["Status"].Value?.ToString() ?? "متوفر";
            
            conditionComboBox.SelectedItem = condition;
            statusComboBox.SelectedItem = status;
        }

        private void ClearForm()
        {
            deviceNameTextBox.Clear();
            imeiTextBox.Clear();
            quantityTextBox.Text = "1";
            purchasePriceTextBox.Clear();
            sellingPriceTextBox.Clear();
            conditionComboBox.SelectedIndex = 0;
            statusComboBox.SelectedIndex = 0;
            updateButton.Enabled = false;
            addButton.Text = "إضافة للمخزون";
            deviceNameTextBox.Focus();
        }

        private void LoadInventory(string search = null)
        {
            try
            {
                var inventory = DatabaseManager.GetInventory(search);
                inventoryDataGridView.DataSource = inventory;

                if (inventoryDataGridView.Columns.Count > 0)
                {
                    inventoryDataGridView.Columns["Id"].HeaderText = "الرقم";
                    inventoryDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                    inventoryDataGridView.Columns["IMEI"].HeaderText = "IMEI";
                    inventoryDataGridView.Columns["Quantity"].HeaderText = "الكمية";
                    inventoryDataGridView.Columns["PurchasePrice"].HeaderText = "سعر الشراء (د.ع)";
                    inventoryDataGridView.Columns["SellingPrice"].HeaderText = "سعر البيع (د.ع)";
                    inventoryDataGridView.Columns["Condition"].HeaderText = "الحالة";
                    inventoryDataGridView.Columns["Status"].HeaderText = "المخزون";
                    
                    inventoryDataGridView.Columns["CreatedAt"].Visible = false;
                    
                    inventoryDataGridView.Columns["PurchasePrice"].DefaultCellStyle.Format = "N0";
                    inventoryDataGridView.Columns["SellingPrice"].DefaultCellStyle.Format = "N0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المخزون:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateSummary()
        {
            try
            {
                var summary = DatabaseManager.GetInventorySummary();
                totalItemsLabel.Text = $"إجمالي الأصناف: {summary.TotalItems}";
                totalValueLabel.Text = $"قيمة المخزون: {summary.TotalValue:N0} د.ع";
                lowStockLabel.Text = $"نفاد المخزون: {summary.LowStockItems}";
            }
            catch (Exception)
            {
                totalItemsLabel.Text = "إجمالي الأصناف: خطأ";
                totalValueLabel.Text = "قيمة المخزون: خطأ";
                lowStockLabel.Text = "نفاد المخزون: خطأ";
            }
        }
    }
}
