@echo off
setlocal enabledelayedexpansion
title Dubai Mobile Store - Management System

echo ==========================================
echo Dubai Mobile Store Management System
echo ==========================================
echo.

REM Check for Python installations
echo [1/4] Checking Python installation...

REM Try different Python commands
set PYTHON_CMD=
set PYTHON_FOUND=0

REM Test py command
py --version >nul 2>&1
if !errorlevel! equ 0 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    echo     Found: py command
    goto :python_found
)

REM Test python command
python --version >nul 2>&1
if !errorlevel! equ 0 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    echo     Found: python command
    goto :python_found
)

REM Test python3 command
python3 --version >nul 2>&1
if !errorlevel! equ 0 (
    set PYTHON_CMD=python3
    set PYTHON_FOUND=1
    echo     Found: python3 command
    goto :python_found
)

REM Check common Python installation paths
echo     Checking common installation paths...
for %%P in (
    "C:\Python39\python.exe"
    "C:\Python310\python.exe"
    "C:\Python311\python.exe"
    "C:\Python312\python.exe"
    "C:\Python313\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python39\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python310\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python311\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python312\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python313\python.exe"
    "%APPDATA%\Local\Programs\Python\Python39\python.exe"
    "%APPDATA%\Local\Programs\Python\Python310\python.exe"
    "%APPDATA%\Local\Programs\Python\Python311\python.exe"
    "%APPDATA%\Local\Programs\Python\Python312\python.exe"
    "%APPDATA%\Local\Programs\Python\Python313\python.exe"
) do (
    if exist %%P (
        set PYTHON_CMD=%%P
        set PYTHON_FOUND=1
        echo     Found: %%P
        goto :python_found
    )
)

:python_not_found
echo.
echo ERROR: Python not found!
echo.
echo Solutions:
echo 1. Install Python from https://python.org
echo 2. Make sure to check "Add Python to PATH" during installation
echo 3. Restart your computer after installation
echo.
echo Alternative: Download Python from Microsoft Store
echo.
pause
exit /b 1

:python_found
echo     SUCCESS: Python found
echo.

echo [2/4] Checking required files...
if not exist "run_simple.py" (
    echo ERROR: run_simple.py not found!
    echo Make sure you're in the correct directory
    pause
    exit /b 1
)
if not exist "database.py" (
    echo ERROR: database.py not found!
    pause
    exit /b 1
)
if not exist "models.py" (
    echo ERROR: models.py not found!
    pause
    exit /b 1
)
echo     SUCCESS: All required files found
echo.

echo [3/4] Creating necessary directories...
if not exist "reports" mkdir reports
if not exist "assets" mkdir assets
if not exist "backups" mkdir backups
echo     SUCCESS: Directories ready
echo.

echo [4/4] Starting application...
echo.
echo ==========================================
echo.

REM Start the application
!PYTHON_CMD! run_simple.py

echo.
echo ==========================================
echo Application closed
echo.
pause
