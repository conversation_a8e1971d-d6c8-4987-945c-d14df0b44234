using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;

namespace DubaiMobileStore
{
    public static class DatabaseManager
    {
        private static readonly string DatabasePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DubaiMobileStore", "store.db");

        private static readonly string ConnectionString = $"Data Source={DatabasePath};Version=3;";

        public static void InitializeDatabase()
        {
            // Create directory if it doesn't exist
            var directory = Path.GetDirectoryName(DatabasePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            // Create tables
            CreateTables(connection);
            
            // Insert default settings if not exists
            InsertDefaultSettings(connection);
        }

        private static void CreateTables(SQLiteConnection connection)
        {
            var commands = new[]
            {
                // Sales table
                @"CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerName TEXT NOT NULL,
                    CustomerPhone TEXT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT,
                    Price DECIMAL NOT NULL,
                    Seller TEXT,
                    SaleDate TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL
                )",

                // Purchases table
                @"CREATE TABLE IF NOT EXISTS Purchases (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SupplierName TEXT NOT NULL,
                    SupplierPhone TEXT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT,
                    Price DECIMAL NOT NULL,
                    PurchaseDate TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL
                )",

                // Expenses table
                @"CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ExpenseType TEXT NOT NULL,
                    Amount DECIMAL NOT NULL,
                    ExpenseDate TEXT NOT NULL,
                    Notes TEXT,
                    CreatedAt TEXT NOT NULL
                )",

                // Inventory table
                @"CREATE TABLE IF NOT EXISTS Inventory (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT UNIQUE,
                    Quantity INTEGER NOT NULL DEFAULT 1,
                    PurchasePrice DECIMAL NOT NULL,
                    SellingPrice DECIMAL NOT NULL,
                    Condition TEXT NOT NULL DEFAULT 'جديد',
                    Status TEXT NOT NULL DEFAULT 'متوفر',
                    CreatedAt TEXT NOT NULL
                )",

                // Store Settings table
                @"CREATE TABLE IF NOT EXISTS StoreSettings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    StoreName TEXT NOT NULL,
                    Phone TEXT,
                    Address TEXT,
                    Email TEXT,
                    LogoPath TEXT,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NOT NULL
                )"
            };

            foreach (var commandText in commands)
            {
                using var command = new SQLiteCommand(commandText, connection);
                command.ExecuteNonQuery();
            }
        }

        private static void InsertDefaultSettings(SQLiteConnection connection)
        {
            const string checkQuery = "SELECT COUNT(*) FROM StoreSettings";
            using var checkCommand = new SQLiteCommand(checkQuery, connection);
            var count = Convert.ToInt32(checkCommand.ExecuteScalar());

            if (count == 0)
            {
                var settings = new StoreSettings
                {
                    StoreName = "معرض دبي للموبايلات",
                    Phone = "01234567890",
                    Address = "شارع الملك فهد، الرياض",
                    Email = "<EMAIL>"
                };

                SaveStoreSettings(settings);
            }
        }

        // Sales operations
        public static int AddSale(Sale sale)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO Sales (CustomerName, CustomerPhone, DeviceName, IMEI, Price, Seller, SaleDate, CreatedAt)
                VALUES (@CustomerName, @CustomerPhone, @DeviceName, @IMEI, @Price, @Seller, @SaleDate, @CreatedAt)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@CustomerName", sale.CustomerName);
            command.Parameters.AddWithValue("@CustomerPhone", sale.CustomerPhone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DeviceName", sale.DeviceName);
            command.Parameters.AddWithValue("@IMEI", sale.IMEI ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Price", sale.Price);
            command.Parameters.AddWithValue("@Seller", sale.Seller ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SaleDate", sale.SaleDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@CreatedAt", sale.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            // Get the last inserted ID
            const string lastIdQuery = "SELECT last_insert_rowid()";
            using var lastIdCommand = new SQLiteCommand(lastIdQuery, connection);
            var saleId = Convert.ToInt32(lastIdCommand.ExecuteScalar());

            // Update inventory status if IMEI is provided
            if (!string.IsNullOrEmpty(sale.IMEI))
            {
                const string updateInventoryQuery = "UPDATE Inventory SET Status = 'مباع' WHERE IMEI = @IMEI";
                using var updateCommand = new SQLiteCommand(updateInventoryQuery, connection);
                updateCommand.Parameters.AddWithValue("@IMEI", sale.IMEI);
                updateCommand.ExecuteNonQuery();
            }

            return saleId;
        }

        public static List<Sale> GetSales(int? limit = null, string? search = null, 
            DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT * FROM Sales WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(search))
            {
                query += " AND (CustomerName LIKE @search OR DeviceName LIKE @search OR IMEI LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{search}%"));
            }

            if (startDate.HasValue)
            {
                query += " AND SaleDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND SaleDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            query += " ORDER BY CreatedAt DESC";

            if (limit.HasValue)
            {
                query += $" LIMIT {limit.Value}";
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            using var reader = command.ExecuteReader();
            var sales = new List<Sale>();

            while (reader.Read())
            {
                sales.Add(new Sale
                {
                    Id = reader.GetInt32("Id"),
                    CustomerName = reader.GetString("CustomerName"),
                    CustomerPhone = reader.IsDBNull("CustomerPhone") ? null : reader.GetString("CustomerPhone"),
                    DeviceName = reader.GetString("DeviceName"),
                    IMEI = reader.IsDBNull("IMEI") ? null : reader.GetString("IMEI"),
                    Price = reader.GetDecimal("Price"),
                    Seller = reader.IsDBNull("Seller") ? null : reader.GetString("Seller"),
                    SaleDate = DateTime.Parse(reader.GetString("SaleDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return sales;
        }

        // Store Settings operations
        public static StoreSettings? GetStoreSettings()
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = "SELECT * FROM StoreSettings ORDER BY Id DESC LIMIT 1";
            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();

            if (reader.Read())
            {
                return new StoreSettings
                {
                    Id = reader.GetInt32("Id"),
                    StoreName = reader.GetString("StoreName"),
                    Phone = reader.IsDBNull("Phone") ? null : reader.GetString("Phone"),
                    Address = reader.IsDBNull("Address") ? null : reader.GetString("Address"),
                    Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                    LogoPath = reader.IsDBNull("LogoPath") ? null : reader.GetString("LogoPath"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt")),
                    UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"))
                };
            }

            return null;
        }

        public static void SaveStoreSettings(StoreSettings settings)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            if (settings.Id > 0)
            {
                // Update existing settings
                const string query = @"
                    UPDATE StoreSettings 
                    SET StoreName = @StoreName, Phone = @Phone, Address = @Address, 
                        Email = @Email, LogoPath = @LogoPath, UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", settings.Id);
                command.Parameters.AddWithValue("@StoreName", settings.StoreName);
                command.Parameters.AddWithValue("@Phone", settings.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", settings.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", settings.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LogoPath", settings.LogoPath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                command.ExecuteNonQuery();
            }
            else
            {
                // Insert new settings
                const string query = @"
                    INSERT INTO StoreSettings (StoreName, Phone, Address, Email, LogoPath, CreatedAt, UpdatedAt)
                    VALUES (@StoreName, @Phone, @Address, @Email, @LogoPath, @CreatedAt, @UpdatedAt)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@StoreName", settings.StoreName);
                command.Parameters.AddWithValue("@Phone", settings.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", settings.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", settings.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LogoPath", settings.LogoPath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                command.ExecuteNonQuery();
            }
        }

        // Statistics
        public static Statistics GetStatistics(DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var whereClause = "WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (startDate.HasValue)
            {
                whereClause += " AND SaleDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                whereClause += " AND SaleDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            var stats = new Statistics();

            // Sales statistics
            var salesQuery = $"SELECT COUNT(*), COALESCE(SUM(Price), 0) FROM Sales {whereClause}";
            using var salesCommand = new SQLiteCommand(salesQuery, connection);
            foreach (var param in parameters)
            {
                salesCommand.Parameters.Add(new SQLiteParameter(param.ParameterName, param.Value));
            }
            using var salesReader = salesCommand.ExecuteReader();
            if (salesReader.Read())
            {
                stats.SalesCount = salesReader.GetInt32(0);
                stats.SalesTotal = salesReader.GetDecimal(1);
            }

            return stats;
        }
    }
}
