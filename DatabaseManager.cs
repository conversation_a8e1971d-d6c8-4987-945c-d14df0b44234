using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;

namespace DubaiMobileStore
{
    public static class DatabaseManager
    {
        private static readonly string DatabasePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DubaiMobileStore", "store.db");

        private static readonly string ConnectionString = $"Data Source={DatabasePath};Version=3;";

        public static void InitializeDatabase()
        {
            // Create directory if it doesn't exist
            var directory = Path.GetDirectoryName(DatabasePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            // Create tables
            CreateTables(connection);
            
            // Insert default settings if not exists
            InsertDefaultSettings(connection);
        }

        private static void CreateTables(SQLiteConnection connection)
        {
            var commands = new[]
            {
                // Sales table
                @"CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerName TEXT NOT NULL,
                    CustomerPhone TEXT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT,
                    Price DECIMAL NOT NULL,
                    Seller TEXT,
                    SaleDate TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL
                )",

                // Purchases table
                @"CREATE TABLE IF NOT EXISTS Purchases (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SupplierName TEXT NOT NULL,
                    SupplierPhone TEXT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT,
                    Price DECIMAL NOT NULL,
                    PurchaseDate TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL
                )",

                // Expenses table
                @"CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ExpenseType TEXT NOT NULL,
                    Amount DECIMAL NOT NULL,
                    ExpenseDate TEXT NOT NULL,
                    Notes TEXT,
                    CreatedAt TEXT NOT NULL
                )",

                // Inventory table
                @"CREATE TABLE IF NOT EXISTS Inventory (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT UNIQUE,
                    Quantity INTEGER NOT NULL DEFAULT 1,
                    PurchasePrice DECIMAL NOT NULL,
                    SellingPrice DECIMAL NOT NULL,
                    Condition TEXT NOT NULL DEFAULT 'جديد',
                    Status TEXT NOT NULL DEFAULT 'متوفر',
                    CreatedAt TEXT NOT NULL
                )",

                // Store Settings table
                @"CREATE TABLE IF NOT EXISTS StoreSettings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    StoreName TEXT NOT NULL,
                    Phone TEXT,
                    Address TEXT,
                    Email TEXT,
                    LogoPath TEXT,
                    Currency TEXT DEFAULT 'AED',
                    CurrencySymbol TEXT DEFAULT 'د.إ',
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NOT NULL
                )",

                // Returns table
                @"CREATE TABLE IF NOT EXISTS Returns (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerName TEXT NOT NULL,
                    CustomerPhone TEXT,
                    DeviceName TEXT NOT NULL,
                    IMEI TEXT,
                    Amount DECIMAL NOT NULL,
                    ReturnReason TEXT NOT NULL,
                    Notes TEXT,
                    OriginalSaleId INTEGER,
                    ReturnDate TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL
                )"
            };

            foreach (var commandText in commands)
            {
                using var command = new SQLiteCommand(commandText, connection);
                command.ExecuteNonQuery();
            }
        }

        private static void InsertDefaultSettings(SQLiteConnection connection)
        {
            const string checkQuery = "SELECT COUNT(*) FROM StoreSettings";
            using var checkCommand = new SQLiteCommand(checkQuery, connection);
            var count = Convert.ToInt32(checkCommand.ExecuteScalar());

            if (count == 0)
            {
                var settings = new StoreSettings
                {
                    StoreName = "معرض دبي للموبايلات",
                    Phone = "0501234567",
                    Address = "شارع الشيخ زايد، دبي، الإمارات العربية المتحدة",
                    Email = "<EMAIL>",
                    Currency = "AED",
                    CurrencySymbol = "د.إ"
                };

                SaveStoreSettings(settings);
            }
        }

        // Sales operations
        public static int AddSale(Sale sale)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO Sales (CustomerName, CustomerPhone, DeviceName, IMEI, Price, Seller, SaleDate, CreatedAt)
                VALUES (@CustomerName, @CustomerPhone, @DeviceName, @IMEI, @Price, @Seller, @SaleDate, @CreatedAt)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@CustomerName", sale.CustomerName);
            command.Parameters.AddWithValue("@CustomerPhone", sale.CustomerPhone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DeviceName", sale.DeviceName);
            command.Parameters.AddWithValue("@IMEI", sale.IMEI ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Price", sale.Price);
            command.Parameters.AddWithValue("@Seller", sale.Seller ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SaleDate", sale.SaleDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@CreatedAt", sale.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            // Get the last inserted ID
            const string lastIdQuery = "SELECT last_insert_rowid()";
            using var lastIdCommand = new SQLiteCommand(lastIdQuery, connection);
            var saleId = Convert.ToInt32(lastIdCommand.ExecuteScalar());

            // Update inventory status if IMEI is provided
            if (!string.IsNullOrEmpty(sale.IMEI))
            {
                const string updateInventoryQuery = "UPDATE Inventory SET Status = 'مباع' WHERE IMEI = @IMEI";
                using var updateCommand = new SQLiteCommand(updateInventoryQuery, connection);
                updateCommand.Parameters.AddWithValue("@IMEI", sale.IMEI);
                updateCommand.ExecuteNonQuery();
            }

            return saleId;
        }

        public static List<Sale> GetSales(int? limit = null, string? search = null, 
            DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT * FROM Sales WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(search))
            {
                query += " AND (CustomerName LIKE @search OR DeviceName LIKE @search OR IMEI LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{search}%"));
            }

            if (startDate.HasValue)
            {
                query += " AND SaleDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND SaleDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            query += " ORDER BY CreatedAt DESC";

            if (limit.HasValue)
            {
                query += $" LIMIT {limit.Value}";
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            using var reader = command.ExecuteReader();
            var sales = new List<Sale>();

            while (reader.Read())
            {
                sales.Add(new Sale
                {
                    Id = reader.GetInt32("Id"),
                    CustomerName = reader.GetString("CustomerName"),
                    CustomerPhone = reader.IsDBNull("CustomerPhone") ? null : reader.GetString("CustomerPhone"),
                    DeviceName = reader.GetString("DeviceName"),
                    IMEI = reader.IsDBNull("IMEI") ? null : reader.GetString("IMEI"),
                    Price = reader.GetDecimal("Price"),
                    Seller = reader.IsDBNull("Seller") ? null : reader.GetString("Seller"),
                    SaleDate = DateTime.Parse(reader.GetString("SaleDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return sales;
        }

        // Store Settings operations
        public static StoreSettings? GetStoreSettings()
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = "SELECT * FROM StoreSettings ORDER BY Id DESC LIMIT 1";
            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();

            if (reader.Read())
            {
                return new StoreSettings
                {
                    Id = reader.GetInt32("Id"),
                    StoreName = reader.GetString("StoreName"),
                    Phone = reader.IsDBNull("Phone") ? null : reader.GetString("Phone"),
                    Address = reader.IsDBNull("Address") ? null : reader.GetString("Address"),
                    Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                    LogoPath = reader.IsDBNull("LogoPath") ? null : reader.GetString("LogoPath"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt")),
                    UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"))
                };
            }

            return null;
        }

        public static void SaveStoreSettings(StoreSettings settings)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            if (settings.Id > 0)
            {
                // Update existing settings
                const string query = @"
                    UPDATE StoreSettings 
                    SET StoreName = @StoreName, Phone = @Phone, Address = @Address, 
                        Email = @Email, LogoPath = @LogoPath, UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", settings.Id);
                command.Parameters.AddWithValue("@StoreName", settings.StoreName);
                command.Parameters.AddWithValue("@Phone", settings.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", settings.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", settings.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LogoPath", settings.LogoPath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                command.ExecuteNonQuery();
            }
            else
            {
                // Insert new settings
                const string query = @"
                    INSERT INTO StoreSettings (StoreName, Phone, Address, Email, LogoPath, CreatedAt, UpdatedAt)
                    VALUES (@StoreName, @Phone, @Address, @Email, @LogoPath, @CreatedAt, @UpdatedAt)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@StoreName", settings.StoreName);
                command.Parameters.AddWithValue("@Phone", settings.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", settings.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", settings.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LogoPath", settings.LogoPath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                command.ExecuteNonQuery();
            }
        }

        // Purchase operations
        public static int AddPurchase(Purchase purchase)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO Purchases (SupplierName, SupplierPhone, DeviceName, IMEI, Price, PurchaseDate, CreatedAt)
                VALUES (@SupplierName, @SupplierPhone, @DeviceName, @IMEI, @Price, @PurchaseDate, @CreatedAt)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@SupplierName", purchase.SupplierName);
            command.Parameters.AddWithValue("@SupplierPhone", purchase.SupplierPhone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DeviceName", purchase.DeviceName);
            command.Parameters.AddWithValue("@IMEI", purchase.IMEI ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Price", purchase.Price);
            command.Parameters.AddWithValue("@PurchaseDate", purchase.PurchaseDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@CreatedAt", purchase.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            // Get the last inserted ID
            const string lastIdQuery = "SELECT last_insert_rowid()";
            using var lastIdCommand = new SQLiteCommand(lastIdQuery, connection);
            return Convert.ToInt32(lastIdCommand.ExecuteScalar());
        }

        public static List<Purchase> GetPurchases(int? limit = null, string? search = null,
            DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT * FROM Purchases WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(search))
            {
                query += " AND (SupplierName LIKE @search OR DeviceName LIKE @search OR IMEI LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{search}%"));
            }

            if (startDate.HasValue)
            {
                query += " AND PurchaseDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND PurchaseDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            query += " ORDER BY CreatedAt DESC";

            if (limit.HasValue)
            {
                query += $" LIMIT {limit.Value}";
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            using var reader = command.ExecuteReader();
            var purchases = new List<Purchase>();

            while (reader.Read())
            {
                purchases.Add(new Purchase
                {
                    Id = reader.GetInt32("Id"),
                    SupplierName = reader.GetString("SupplierName"),
                    SupplierPhone = reader.IsDBNull("SupplierPhone") ? null : reader.GetString("SupplierPhone"),
                    DeviceName = reader.GetString("DeviceName"),
                    IMEI = reader.IsDBNull("IMEI") ? null : reader.GetString("IMEI"),
                    Price = reader.GetDecimal("Price"),
                    PurchaseDate = DateTime.Parse(reader.GetString("PurchaseDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return purchases;
        }

        // Expense operations
        public static int AddExpense(Expense expense)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO Expenses (ExpenseType, Amount, ExpenseDate, Notes, CreatedAt)
                VALUES (@ExpenseType, @Amount, @ExpenseDate, @Notes, @CreatedAt)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@ExpenseType", expense.ExpenseType);
            command.Parameters.AddWithValue("@Amount", expense.Amount);
            command.Parameters.AddWithValue("@ExpenseDate", expense.ExpenseDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@Notes", expense.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CreatedAt", expense.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            // Get the last inserted ID
            const string lastIdQuery = "SELECT last_insert_rowid()";
            using var lastIdCommand = new SQLiteCommand(lastIdQuery, connection);
            return Convert.ToInt32(lastIdCommand.ExecuteScalar());
        }

        public static List<Expense> GetExpenses(int? limit = null, string? search = null,
            DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT * FROM Expenses WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(search))
            {
                query += " AND (ExpenseType LIKE @search OR Notes LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{search}%"));
            }

            if (startDate.HasValue)
            {
                query += " AND ExpenseDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND ExpenseDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            query += " ORDER BY CreatedAt DESC";

            if (limit.HasValue)
            {
                query += $" LIMIT {limit.Value}";
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            using var reader = command.ExecuteReader();
            var expenses = new List<Expense>();

            while (reader.Read())
            {
                expenses.Add(new Expense
                {
                    Id = reader.GetInt32("Id"),
                    ExpenseType = reader.GetString("ExpenseType"),
                    Amount = reader.GetDecimal("Amount"),
                    ExpenseDate = DateTime.Parse(reader.GetString("ExpenseDate")),
                    Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return expenses;
        }

        public static decimal GetTotalExpenses(DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT COALESCE(SUM(Amount), 0) FROM Expenses WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (startDate.HasValue)
            {
                query += " AND ExpenseDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND ExpenseDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            return Convert.ToDecimal(command.ExecuteScalar());
        }

        // Inventory operations
        public static int AddInventory(Inventory inventory)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO Inventory (DeviceName, IMEI, Quantity, PurchasePrice, SellingPrice, Condition, Status, CreatedAt)
                VALUES (@DeviceName, @IMEI, @Quantity, @PurchasePrice, @SellingPrice, @Condition, @Status, @CreatedAt)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@DeviceName", inventory.DeviceName);
            command.Parameters.AddWithValue("@IMEI", inventory.IMEI ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Quantity", inventory.Quantity);
            command.Parameters.AddWithValue("@PurchasePrice", inventory.PurchasePrice);
            command.Parameters.AddWithValue("@SellingPrice", inventory.SellingPrice);
            command.Parameters.AddWithValue("@Condition", inventory.Condition);
            command.Parameters.AddWithValue("@Status", inventory.Status);
            command.Parameters.AddWithValue("@CreatedAt", inventory.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            const string lastIdQuery = "SELECT last_insert_rowid()";
            using var lastIdCommand = new SQLiteCommand(lastIdQuery, connection);
            return Convert.ToInt32(lastIdCommand.ExecuteScalar());
        }

        public static void UpdateInventory(Inventory inventory)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                UPDATE Inventory
                SET DeviceName = @DeviceName, IMEI = @IMEI, Quantity = @Quantity,
                    PurchasePrice = @PurchasePrice, SellingPrice = @SellingPrice,
                    Condition = @Condition, Status = @Status
                WHERE Id = @Id";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@Id", inventory.Id);
            command.Parameters.AddWithValue("@DeviceName", inventory.DeviceName);
            command.Parameters.AddWithValue("@IMEI", inventory.IMEI ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Quantity", inventory.Quantity);
            command.Parameters.AddWithValue("@PurchasePrice", inventory.PurchasePrice);
            command.Parameters.AddWithValue("@SellingPrice", inventory.SellingPrice);
            command.Parameters.AddWithValue("@Condition", inventory.Condition);
            command.Parameters.AddWithValue("@Status", inventory.Status);

            command.ExecuteNonQuery();
        }

        public static List<Inventory> GetInventory(string? search = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT * FROM Inventory WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(search))
            {
                query += " AND (DeviceName LIKE @search OR IMEI LIKE @search OR Condition LIKE @search OR Status LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{search}%"));
            }

            query += " ORDER BY CreatedAt DESC";

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            using var reader = command.ExecuteReader();
            var inventory = new List<Inventory>();

            while (reader.Read())
            {
                inventory.Add(new Inventory
                {
                    Id = reader.GetInt32("Id"),
                    DeviceName = reader.GetString("DeviceName"),
                    IMEI = reader.IsDBNull("IMEI") ? null : reader.GetString("IMEI"),
                    Quantity = reader.GetInt32("Quantity"),
                    PurchasePrice = reader.GetDecimal("PurchasePrice"),
                    SellingPrice = reader.GetDecimal("SellingPrice"),
                    Condition = reader.GetString("Condition"),
                    Status = reader.GetString("Status"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return inventory;
        }

        public static InventorySummary GetInventorySummary()
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var summary = new InventorySummary();

            // Total items
            const string totalQuery = "SELECT COUNT(*), COALESCE(SUM(Quantity * PurchasePrice), 0) FROM Inventory";
            using var totalCommand = new SQLiteCommand(totalQuery, connection);
            using var totalReader = totalCommand.ExecuteReader();
            if (totalReader.Read())
            {
                summary.TotalItems = totalReader.GetInt32(0);
                summary.TotalValue = totalReader.GetDecimal(1);
            }

            // Low stock items (quantity <= 2)
            const string lowStockQuery = "SELECT COUNT(*) FROM Inventory WHERE Quantity <= 2 AND Status = 'متوفر'";
            using var lowStockCommand = new SQLiteCommand(lowStockQuery, connection);
            summary.LowStockItems = Convert.ToInt32(lowStockCommand.ExecuteScalar());

            return summary;
        }

        // Return operations
        public static int AddReturn(Return returnItem)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            const string query = @"
                INSERT INTO Returns (CustomerName, CustomerPhone, DeviceName, IMEI, Amount, ReturnReason, Notes, OriginalSaleId, ReturnDate, CreatedAt)
                VALUES (@CustomerName, @CustomerPhone, @DeviceName, @IMEI, @Amount, @ReturnReason, @Notes, @OriginalSaleId, @ReturnDate, @CreatedAt)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@CustomerName", returnItem.CustomerName);
            command.Parameters.AddWithValue("@CustomerPhone", returnItem.CustomerPhone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DeviceName", returnItem.DeviceName);
            command.Parameters.AddWithValue("@IMEI", returnItem.IMEI ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Amount", returnItem.Amount);
            command.Parameters.AddWithValue("@ReturnReason", returnItem.ReturnReason);
            command.Parameters.AddWithValue("@Notes", returnItem.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@OriginalSaleId", returnItem.OriginalSaleId ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ReturnDate", returnItem.ReturnDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@CreatedAt", returnItem.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            const string lastIdQuery = "SELECT last_insert_rowid()";
            using var lastIdCommand = new SQLiteCommand(lastIdQuery, connection);
            return Convert.ToInt32(lastIdCommand.ExecuteScalar());
        }

        public static List<Return> GetReturns(int? limit = null, string? search = null,
            DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT * FROM Returns WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(search))
            {
                query += " AND (CustomerName LIKE @search OR DeviceName LIKE @search OR IMEI LIKE @search OR ReturnReason LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{search}%"));
            }

            if (startDate.HasValue)
            {
                query += " AND ReturnDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND ReturnDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            query += " ORDER BY CreatedAt DESC";

            if (limit.HasValue)
            {
                query += $" LIMIT {limit.Value}";
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            using var reader = command.ExecuteReader();
            var returns = new List<Return>();

            while (reader.Read())
            {
                returns.Add(new Return
                {
                    Id = reader.GetInt32("Id"),
                    CustomerName = reader.GetString("CustomerName"),
                    CustomerPhone = reader.IsDBNull("CustomerPhone") ? null : reader.GetString("CustomerPhone"),
                    DeviceName = reader.GetString("DeviceName"),
                    IMEI = reader.IsDBNull("IMEI") ? null : reader.GetString("IMEI"),
                    Amount = reader.GetDecimal("Amount"),
                    ReturnReason = reader.GetString("ReturnReason"),
                    Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                    OriginalSaleId = reader.IsDBNull("OriginalSaleId") ? null : reader.GetInt32("OriginalSaleId"),
                    ReturnDate = DateTime.Parse(reader.GetString("ReturnDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return returns;
        }

        public static decimal GetTotalReturns(DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var query = "SELECT COALESCE(SUM(Amount), 0) FROM Returns WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (startDate.HasValue)
            {
                query += " AND ReturnDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                query += " AND ReturnDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            using var command = new SQLiteCommand(query, connection);
            foreach (var parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }

            return Convert.ToDecimal(command.ExecuteScalar());
        }

        // Statistics
        public static Statistics GetStatistics(DateTime? startDate = null, DateTime? endDate = null)
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var whereClause = "WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (startDate.HasValue)
            {
                whereClause += " AND SaleDate >= @startDate";
                parameters.Add(new SQLiteParameter("@startDate", startDate.Value.ToString("yyyy-MM-dd")));
            }

            if (endDate.HasValue)
            {
                whereClause += " AND SaleDate <= @endDate";
                parameters.Add(new SQLiteParameter("@endDate", endDate.Value.ToString("yyyy-MM-dd")));
            }

            var stats = new Statistics();

            // Sales statistics
            var salesQuery = $"SELECT COUNT(*), COALESCE(SUM(Price), 0) FROM Sales {whereClause}";
            using var salesCommand = new SQLiteCommand(salesQuery, connection);
            foreach (var param in parameters)
            {
                salesCommand.Parameters.Add(new SQLiteParameter(param.ParameterName, param.Value));
            }
            using var salesReader = salesCommand.ExecuteReader();
            if (salesReader.Read())
            {
                stats.SalesCount = salesReader.GetInt32(0);
                stats.SalesTotal = salesReader.GetDecimal(1);
            }

            // Purchases statistics
            var purchasesQuery = $"SELECT COUNT(*), COALESCE(SUM(Price), 0) FROM Purchases {whereClause.Replace("SaleDate", "PurchaseDate")}";
            using var purchasesCommand = new SQLiteCommand(purchasesQuery, connection);
            foreach (var param in parameters)
            {
                var newParam = new SQLiteParameter(param.ParameterName, param.Value);
                purchasesCommand.Parameters.Add(newParam);
            }
            using var purchasesReader = purchasesCommand.ExecuteReader();
            if (purchasesReader.Read())
            {
                stats.PurchasesCount = purchasesReader.GetInt32(0);
                stats.PurchasesTotal = purchasesReader.GetDecimal(1);
            }

            // Expenses statistics
            var expensesQuery = $"SELECT COUNT(*), COALESCE(SUM(Amount), 0) FROM Expenses {whereClause.Replace("SaleDate", "ExpenseDate")}";
            using var expensesCommand = new SQLiteCommand(expensesQuery, connection);
            foreach (var param in parameters)
            {
                var newParam = new SQLiteParameter(param.ParameterName, param.Value);
                expensesCommand.Parameters.Add(newParam);
            }
            using var expensesReader = expensesCommand.ExecuteReader();
            if (expensesReader.Read())
            {
                stats.ExpensesCount = expensesReader.GetInt32(0);
                stats.ExpensesTotal = expensesReader.GetDecimal(1);
            }

            // Returns statistics
            var returnsQuery = $"SELECT COUNT(*), COALESCE(SUM(Amount), 0) FROM Returns {whereClause.Replace("SaleDate", "ReturnDate")}";
            using var returnsCommand = new SQLiteCommand(returnsQuery, connection);
            foreach (var param in parameters)
            {
                var newParam = new SQLiteParameter(param.ParameterName, param.Value);
                returnsCommand.Parameters.Add(newParam);
            }
            using var returnsReader = returnsCommand.ExecuteReader();
            if (returnsReader.Read())
            {
                stats.ReturnsCount = returnsReader.GetInt32(0);
                stats.ReturnsTotal = returnsReader.GetDecimal(1);
            }

            return stats;
        }

        // System operations
        public static void ClearAllData()
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            var commands = new[]
            {
                "DELETE FROM Sales",
                "DELETE FROM Purchases",
                "DELETE FROM Expenses",
                "DELETE FROM Returns",
                "DELETE FROM Inventory"
            };

            foreach (var commandText in commands)
            {
                using var command = new SQLiteCommand(commandText, connection);
                command.ExecuteNonQuery();
            }
        }
    }
}
