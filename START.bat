@echo off
chcp 65001 > nul
title Dubai Mobile Store Management System

echo ========================================
echo Dubai Mobile Store Management System
echo ========================================
echo.

echo Checking Python...
py --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not installed or not available
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python is available
echo.

echo Starting the application...
echo.

REM Try GUI first
py main.py
if %errorlevel% neq 0 (
    echo.
    echo GUI failed, starting text mode...
    echo.
    py run_simple.py
)

echo.
echo Application ended
pause
