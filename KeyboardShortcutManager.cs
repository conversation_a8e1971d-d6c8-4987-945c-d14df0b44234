using System;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public static class KeyboardShortcutManager
    {
        public static void RegisterShortcuts(Form mainForm)
        {
            mainForm.KeyPreview = true;
            mainForm.KeyDown += MainForm_KeyDown;
        }

        private static void MainForm_KeyDown(object sender, KeyEventArgs e)
        {
            // Handle keyboard shortcuts
            if (e.Control)
            {
                switch (e.KeyCode)
                {
                    case Keys.H: // Ctrl+H - Home/Dashboard
                        OnNavigateToHome?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.S: // Ctrl+S - Sales
                        OnNavigateToSales?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.P: // Ctrl+P - Purchases
                        OnNavigateToPurchases?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.E: // Ctrl+E - Expenses
                        OnNavigateToExpenses?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.R: // Ctrl+R - Returns
                        OnNavigateToReturns?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.I: // Ctrl+I - Inventory
                        OnNavigateToInventory?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.T: // Ctrl+T - Reports
                        OnNavigateToReports?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.G: // Ctrl+G - Settings
                        OnNavigateToSettings?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.N: // Ctrl+N - New Sale
                        OnNewSale?.Invoke();
                        e.Handled = true;
                        break;

                    case Keys.B: // Ctrl+B - Backup
                        AutoBackupManager.CreateManualBackup("QuickBackup");
                        e.Handled = true;
                        break;

                    case Keys.F: // Ctrl+F - Search
                        OnFocusSearch?.Invoke();
                        e.Handled = true;
                        break;
                }
            }
            else if (e.KeyCode == Keys.F1) // F1 - Help
            {
                ShowHelpDialog();
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.F5) // F5 - Refresh
            {
                OnRefreshCurrentView?.Invoke();
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.Escape) // Escape - Clear/Cancel
            {
                OnClearForm?.Invoke();
                e.Handled = true;
            }
        }

        private static void ShowHelpDialog()
        {
            var helpForm = new Form
            {
                Text = "اختصارات لوحة المفاتيح",
                Size = new Size(500, 600),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes
            };

            var helpText = @"🎯 اختصارات التنقل:
Ctrl + H = الصفحة الرئيسية
Ctrl + S = المبيعات
Ctrl + P = المشتريات
Ctrl + E = المصروفات
Ctrl + R = المرتجعات
Ctrl + I = المخزون
Ctrl + T = التقارير
Ctrl + G = الإعدادات

⚡ اختصارات سريعة:
Ctrl + N = بيع جديد
Ctrl + F = البحث
Ctrl + B = نسخة احتياطية
F5 = تحديث
F1 = المساعدة
Escape = مسح/إلغاء

💡 نصائح:
• استخدم Tab للتنقل بين الحقول
• استخدم Enter لتأكيد الإدخال
• استخدم Escape للإلغاء
• استخدم Ctrl+F للبحث السريع";

            var textBox = new TextBox
            {
                Text = helpText,
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F),
                BackColor = System.Drawing.Color.White
            };

            var closeButton = new Button
            {
                Text = "إغلاق",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(400, 530),
                DialogResult = DialogResult.OK
            };

            helpForm.Controls.Add(textBox);
            helpForm.Controls.Add(closeButton);
            helpForm.AcceptButton = closeButton;

            helpForm.ShowDialog();
        }

        // Events for navigation
        public static event Action OnNavigateToHome;
        public static event Action OnNavigateToSales;
        public static event Action OnNavigateToPurchases;
        public static event Action OnNavigateToExpenses;
        public static event Action OnNavigateToReturns;
        public static event Action OnNavigateToInventory;
        public static event Action OnNavigateToReports;
        public static event Action OnNavigateToSettings;
        public static event Action OnNewSale;
        public static event Action OnFocusSearch;
        public static event Action OnRefreshCurrentView;
        public static event Action OnClearForm;
    }
}
