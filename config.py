# -*- coding: utf-8 -*-
"""
إعدادات البرنامج
"""

import os

# إعدادات قاعدة البيانات
DATABASE_NAME = "dubai_mobile_store.db"

# إعدادات المحل الافتراضية
DEFAULT_STORE_NAME = "معرض دبي للموبايلات"
DEFAULT_STORE_PHONE = "01234567890"
DEFAULT_STORE_ADDRESS = "شارع الملك فهد، الرياض"
DEFAULT_STORE_EMAIL = "<EMAIL>"

# إعدادات الواجهة
WINDOW_TITLE = "برنامج حسابات معرض دبي للموبايلات"
WINDOW_SIZE = "1200x800"
WINDOW_MIN_SIZE = (1000, 600)

# ألوان التصميم
COLORS = {
    'primary': '#2E86AB',      # أزرق
    'secondary': '#A23B72',    # بنفسجي
    'success': '#F18F01',      # برتقالي
    'danger': '#C73E1D',       # أحمر
    'warning': '#F4D03F',      # أصفر
    'light': '#F8F9FA',        # رمادي فاتح
    'dark': '#343A40',         # رمادي غامق
    'white': '#FFFFFF',
    'background': '#F5F5F5'
}

# مسارات الملفات
ASSETS_DIR = "assets"
LOGO_PATH = os.path.join(ASSETS_DIR, "logo.png")
REPORTS_DIR = "reports"
BACKUPS_DIR = "backups"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [ASSETS_DIR, REPORTS_DIR, BACKUPS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# إعدادات التقارير
REPORT_FORMATS = ['PDF', 'Excel']
DATE_FORMAT = "%Y-%m-%d"
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

# إعدادات الطباعة
INVOICE_TEMPLATE = {
    'header_height': 100,
    'footer_height': 50,
    'margin': 50,
    'line_height': 20
}
