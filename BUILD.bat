@echo off
title Building Dubai Mobile Store Application

echo ==========================================
echo Building Dubai Mobile Store Application
echo ==========================================
echo.

REM Check for .NET SDK
echo [1/4] Checking .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found!
    echo.
    echo Please install .NET 6.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    pause
    exit /b 1
)

echo     .NET SDK found
dotnet --version
echo.

REM Restore packages
echo [2/4] Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)
echo     Packages restored successfully
echo.

REM Build the application
echo [3/4] Building application...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo     Build completed successfully
echo.

REM Publish the application
echo [4/4] Publishing application...
dotnet publish --configuration Release --output ./publish --self-contained true --runtime win-x64
if %errorlevel% neq 0 (
    echo ERROR: Publish failed
    pause
    exit /b 1
)
echo     Application published successfully
echo.

echo ==========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ==========================================
echo.
echo The application has been built and published to:
echo %cd%\publish\
echo.
echo You can run the application by executing:
echo publish\DubaiMobileStore.exe
echo.

REM Ask if user wants to run the application
set /p choice="Do you want to run the application now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting application...
    start "" "publish\DubaiMobileStore.exe"
)

echo.
pause
