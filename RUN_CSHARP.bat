@echo off
title Dubai Mobile Store - C# Application

echo ==========================================
echo Dubai Mobile Store Management System
echo C# Windows Forms Application
echo ==========================================
echo.

REM Check if published version exists
if exist "publish\DubaiMobileStore.exe" (
    echo Running published version...
    echo.
    start "" "publish\DubaiMobileStore.exe"
    goto :end
)

REM Check for .NET SDK
echo Checking .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found!
    echo.
    echo Please install .NET 6.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo Or run BUILD.bat to build the application first
    pause
    exit /b 1
)

echo .NET SDK found
echo.

REM Run the application in development mode
echo Running application in development mode...
echo.
dotnet run

:end
echo.
echo Application closed
pause
