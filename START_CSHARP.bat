@echo off
setlocal enabledelayedexpansion
title Dubai Mobile Store - C# Application

echo ==========================================
echo 🏪 معرض دبي للموبايلات
echo Dubai Mobile Store Management System
echo C# Windows Forms Application
echo ==========================================
echo.

REM Check if .NET is installed
echo [1/3] Checking .NET installation...
dotnet --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ .NET SDK not found!
    echo.
    echo 📥 Installing .NET SDK...
    echo Opening download page...
    start "" "https://dotnet.microsoft.com/download/dotnet/6.0"
    echo.
    echo Please:
    echo 1. Download .NET 6.0 SDK
    echo 2. Install it
    echo 3. Restart your computer
    echo 4. Run this file again
    echo.
    pause
    exit /b 1
)

echo ✅ .NET SDK found
dotnet --version
echo.

REM Build the application if needed
echo [2/3] Preparing application...
if not exist "bin\Release\net6.0-windows\DubaiMobileStore.exe" (
    echo Building application...
    dotnet build --configuration Release >nul 2>&1
    if !errorlevel! neq 0 (
        echo ❌ Build failed, trying to restore packages...
        dotnet restore
        dotnet build --configuration Release
        if !errorlevel! neq 0 (
            echo ❌ Build failed
            echo Please check your internet connection and try again
            pause
            exit /b 1
        )
    )
    echo ✅ Application built successfully
) else (
    echo ✅ Application already built
)
echo.

REM Run the application
echo [3/3] Starting application...
echo.
echo 🚀 Launching Dubai Mobile Store...
echo.

REM Try to run the built executable first
if exist "bin\Release\net6.0-windows\DubaiMobileStore.exe" (
    start "" "bin\Release\net6.0-windows\DubaiMobileStore.exe"
) else (
    REM Fallback to dotnet run
    dotnet run --configuration Release
)

echo.
echo ✅ Application started successfully!
echo.
echo 💡 Tips:
echo - The application window should open automatically
echo - If you don't see it, check your taskbar
echo - Close this window after the application opens
echo.
echo 📞 Support: <EMAIL>
echo.
pause
