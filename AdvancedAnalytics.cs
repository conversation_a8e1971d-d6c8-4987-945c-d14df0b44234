using System;
using System.Collections.Generic;
using System.Linq;

namespace DubaiMobileStore
{
    public static class AdvancedAnalytics
    {
        public static SalesAnalytics GetSalesAnalytics(DateTime? startDate = null, DateTime? endDate = null)
        {
            var sales = DatabaseManager.GetSales(null, null, startDate, endDate);
            
            return new SalesAnalytics
            {
                TotalSales = sales.Count,
                TotalRevenue = sales.Sum(s => s.Price),
                AverageOrderValue = sales.Any() ? sales.Average(s => s.Price) : 0,
                TopSellingDevices = GetTopSellingDevices(sales),
                SalesByDay = GetSalesByDay(sales),
                SalesByHour = GetSalesByHour(sales),
                CustomerAnalysis = GetCustomerAnalysis(sales),
                ProfitMarginAnalysis = GetProfitMarginAnalysis(sales)
            };
        }

        private static List<DeviceSalesData> GetTopSellingDevices(List<Sale> sales)
        {
            return sales.GroupBy(s => s.DeviceName)
                       .Select(g => new DeviceSalesData
                       {
                           DeviceName = g.Key,
                           Quantity = g.Count(),
                           TotalRevenue = g.Sum(s => s.Price),
                           AveragePrice = g.Average(s => s.Price)
                       })
                       .OrderByDescending(d => d.Quantity)
                       .Take(10)
                       .ToList();
        }

        private static List<DailySalesData> GetSalesByDay(List<Sale> sales)
        {
            return sales.GroupBy(s => s.SaleDate.Date)
                       .Select(g => new DailySalesData
                       {
                           Date = g.Key,
                           SalesCount = g.Count(),
                           Revenue = g.Sum(s => s.Price)
                       })
                       .OrderBy(d => d.Date)
                       .ToList();
        }

        private static List<HourlySalesData> GetSalesByHour(List<Sale> sales)
        {
            return sales.GroupBy(s => s.CreatedAt.Hour)
                       .Select(g => new HourlySalesData
                       {
                           Hour = g.Key,
                           SalesCount = g.Count(),
                           Revenue = g.Sum(s => s.Price)
                       })
                       .OrderBy(h => h.Hour)
                       .ToList();
        }

        private static CustomerAnalysisData GetCustomerAnalysis(List<Sale> sales)
        {
            var customerGroups = sales.GroupBy(s => s.CustomerName).ToList();
            
            return new CustomerAnalysisData
            {
                TotalCustomers = customerGroups.Count,
                RepeatCustomers = customerGroups.Count(g => g.Count() > 1),
                TopCustomers = customerGroups
                    .Select(g => new CustomerData
                    {
                        Name = g.Key,
                        PurchaseCount = g.Count(),
                        TotalSpent = g.Sum(s => s.Price),
                        LastPurchase = g.Max(s => s.SaleDate)
                    })
                    .OrderByDescending(c => c.TotalSpent)
                    .Take(10)
                    .ToList()
            };
        }

        private static ProfitAnalysisData GetProfitMarginAnalysis(List<Sale> sales)
        {
            var purchases = DatabaseManager.GetPurchases();
            var expenses = DatabaseManager.GetExpenses();
            var returns = DatabaseManager.GetReturns();

            var totalRevenue = sales.Sum(s => s.Price);
            var totalCosts = purchases.Sum(p => p.Price);
            var totalExpenses = expenses.Sum(e => e.Amount);
            var totalReturns = returns.Sum(r => r.Amount);

            var grossProfit = totalRevenue - totalCosts;
            var netProfit = grossProfit - totalExpenses - totalReturns;

            return new ProfitAnalysisData
            {
                TotalRevenue = totalRevenue,
                TotalCosts = totalCosts,
                TotalExpenses = totalExpenses,
                TotalReturns = totalReturns,
                GrossProfit = grossProfit,
                NetProfit = netProfit,
                GrossProfitMargin = totalRevenue > 0 ? (double)(grossProfit / totalRevenue) * 100 : 0,
                NetProfitMargin = totalRevenue > 0 ? (double)(netProfit / totalRevenue) * 100 : 0
            };
        }

        public static InventoryAnalytics GetInventoryAnalytics()
        {
            var inventory = DatabaseManager.GetInventory();
            var sales = DatabaseManager.GetSales();

            return new InventoryAnalytics
            {
                TotalItems = inventory.Count,
                TotalValue = inventory.Sum(i => i.SellingPrice * i.Quantity),
                LowStockItems = inventory.Count(i => i.Quantity <= 2),
                OutOfStockItems = inventory.Count(i => i.Quantity == 0),
                FastMovingItems = GetFastMovingItems(inventory, sales),
                SlowMovingItems = GetSlowMovingItems(inventory, sales),
                InventoryTurnover = CalculateInventoryTurnover(inventory, sales)
            };
        }

        private static List<InventoryMovementData> GetFastMovingItems(List<Inventory> inventory, List<Sale> sales)
        {
            var salesByDevice = sales.Where(s => s.SaleDate >= DateTime.Now.AddDays(-30))
                                   .GroupBy(s => s.DeviceName)
                                   .ToDictionary(g => g.Key, g => g.Count());

            return inventory.Select(i => new InventoryMovementData
                          {
                              DeviceName = i.DeviceName,
                              CurrentStock = i.Quantity,
                              SalesLast30Days = salesByDevice.GetValueOrDefault(i.DeviceName, 0),
                              TurnoverRate = i.Quantity > 0 ? (double)salesByDevice.GetValueOrDefault(i.DeviceName, 0) / i.Quantity : 0
                          })
                          .OrderByDescending(i => i.TurnoverRate)
                          .Take(10)
                          .ToList();
        }

        private static List<InventoryMovementData> GetSlowMovingItems(List<Inventory> inventory, List<Sale> sales)
        {
            var salesByDevice = sales.Where(s => s.SaleDate >= DateTime.Now.AddDays(-60))
                                   .GroupBy(s => s.DeviceName)
                                   .ToDictionary(g => g.Key, g => g.Count());

            return inventory.Where(i => i.Quantity > 0)
                          .Select(i => new InventoryMovementData
                          {
                              DeviceName = i.DeviceName,
                              CurrentStock = i.Quantity,
                              SalesLast60Days = salesByDevice.GetValueOrDefault(i.DeviceName, 0),
                              DaysWithoutSale = CalculateDaysWithoutSale(i.DeviceName, sales)
                          })
                          .Where(i => i.DaysWithoutSale > 30)
                          .OrderByDescending(i => i.DaysWithoutSale)
                          .Take(10)
                          .ToList();
        }

        private static int CalculateDaysWithoutSale(string deviceName, List<Sale> sales)
        {
            var lastSale = sales.Where(s => s.DeviceName == deviceName)
                              .OrderByDescending(s => s.SaleDate)
                              .FirstOrDefault();

            return lastSale != null ? (DateTime.Now.Date - lastSale.SaleDate.Date).Days : 999;
        }

        private static double CalculateInventoryTurnover(List<Inventory> inventory, List<Sale> sales)
        {
            var totalInventoryValue = inventory.Sum(i => i.PurchasePrice * i.Quantity);
            var costOfGoodsSold = sales.Where(s => s.SaleDate >= DateTime.Now.AddDays(-365))
                                      .Sum(s => GetCostOfSale(s.DeviceName, s.IMEI, inventory));

            return totalInventoryValue > 0 ? (double)(costOfGoodsSold / totalInventoryValue) : 0;
        }

        private static decimal GetCostOfSale(string deviceName, string imei, List<Inventory> inventory)
        {
            var item = inventory.FirstOrDefault(i => i.DeviceName == deviceName &&
                                                   (string.IsNullOrEmpty(imei) || i.IMEI == imei));
            return item?.PurchasePrice ?? 0;
        }

        public static List<TrendData> GetSalesTrend(int days = 30)
        {
            var startDate = DateTime.Now.AddDays(-days);
            var sales = DatabaseManager.GetSales(null, null, startDate, DateTime.Now);

            return Enumerable.Range(0, days)
                           .Select(i => startDate.AddDays(i))
                           .Select(date => new TrendData
                           {
                               Date = date,
                               Value = sales.Where(s => s.SaleDate.Date == date.Date).Sum(s => s.Price)
                           })
                           .ToList();
        }
    }

    // Data models for analytics
    public class SalesAnalytics
    {
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageOrderValue { get; set; }
        public List<DeviceSalesData> TopSellingDevices { get; set; } = new();
        public List<DailySalesData> SalesByDay { get; set; } = new();
        public List<HourlySalesData> SalesByHour { get; set; } = new();
        public CustomerAnalysisData CustomerAnalysis { get; set; } = new();
        public ProfitAnalysisData ProfitMarginAnalysis { get; set; } = new();
    }

    public class DeviceSalesData
    {
        public string DeviceName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AveragePrice { get; set; }
    }

    public class DailySalesData
    {
        public DateTime Date { get; set; }
        public int SalesCount { get; set; }
        public decimal Revenue { get; set; }
    }

    public class HourlySalesData
    {
        public int Hour { get; set; }
        public int SalesCount { get; set; }
        public decimal Revenue { get; set; }
    }

    public class CustomerAnalysisData
    {
        public int TotalCustomers { get; set; }
        public int RepeatCustomers { get; set; }
        public List<CustomerData> TopCustomers { get; set; } = new();
    }

    public class CustomerData
    {
        public string Name { get; set; } = string.Empty;
        public int PurchaseCount { get; set; }
        public decimal TotalSpent { get; set; }
        public DateTime LastPurchase { get; set; }
    }

    public class ProfitAnalysisData
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal TotalReturns { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public double GrossProfitMargin { get; set; }
        public double NetProfitMargin { get; set; }
    }

    public class InventoryAnalytics
    {
        public int TotalItems { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public List<InventoryMovementData> FastMovingItems { get; set; } = new();
        public List<InventoryMovementData> SlowMovingItems { get; set; } = new();
        public double InventoryTurnover { get; set; }
    }

    public class InventoryMovementData
    {
        public string DeviceName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int SalesLast30Days { get; set; }
        public int SalesLast60Days { get; set; }
        public double TurnoverRate { get; set; }
        public int DaysWithoutSale { get; set; }
    }

    public class TrendData
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
    }
}
