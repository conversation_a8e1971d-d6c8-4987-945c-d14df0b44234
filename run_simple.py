#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لبرنامج حسابات معرض دبي للموبايلات
يعمل بدون واجهة رسومية في حالة وجود مشاكل في tkinter
"""

import sys
import os
from datetime import datetime, date

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main_menu():
    """القائمة الرئيسية"""
    from database import DatabaseManager
    from models import Sale, Purchase, Expense, StoreSettings
    from utils import ReportGenerator
    
    db = DatabaseManager()
    report_gen = ReportGenerator()
    
    while True:
        print("\n" + "="*60)
        print("🏪 برنامج حسابات معرض دبي للموبايلات")
        print("="*60)
        print("1️⃣  إضافة عملية بيع جديدة")
        print("2️⃣  إضافة عملية شراء جديدة")
        print("3️⃣  إضافة مصروف جديد")
        print("4️⃣  عرض المبيعات")
        print("5️⃣  عرض المشتريات")
        print("6️⃣  عرض المصروفات")
        print("7️⃣  عرض الإحصائيات")
        print("8️⃣  إنشاء تقرير")
        print("9️⃣  إعدادات المحل")
        print("0️⃣  خروج")
        print("="*60)
        
        choice = input("👈 اختر رقم العملية: ").strip()
        
        try:
            if choice == "1":
                add_sale(db)
            elif choice == "2":
                add_purchase(db)
            elif choice == "3":
                add_expense(db)
            elif choice == "4":
                show_sales(db)
            elif choice == "5":
                show_purchases(db)
            elif choice == "6":
                show_expenses(db)
            elif choice == "7":
                show_statistics(db)
            elif choice == "8":
                generate_report(db, report_gen)
            elif choice == "9":
                manage_settings(db)
            elif choice == "0":
                print("🙏 شكراً لاستخدام البرنامج!")
                break
            else:
                print("❌ اختيار غير صحيح، حاول مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n🙏 شكراً لاستخدام البرنامج!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
        
        input("\n⏸️  اضغط Enter للمتابعة...")

def add_sale(db):
    """إضافة عملية بيع جديدة"""
    from models import Sale
    
    print("\n📱 إضافة عملية بيع جديدة")
    print("-" * 30)
    
    customer_name = input("اسم العميل: ").strip()
    if not customer_name:
        print("❌ اسم العميل مطلوب")
        return
    
    customer_phone = input("رقم هاتف العميل (اختياري): ").strip()
    device_name = input("اسم الجهاز: ").strip()
    if not device_name:
        print("❌ اسم الجهاز مطلوب")
        return
    
    imei = input("رقم IMEI (اختياري): ").strip()
    
    try:
        price = float(input("السعر: ").strip())
        if price <= 0:
            print("❌ السعر يجب أن يكون أكبر من صفر")
            return
    except ValueError:
        print("❌ السعر غير صحيح")
        return
    
    seller = input("اسم البائع (اختياري): ").strip()
    sale_date = input(f"تاريخ البيع (اتركه فارغ لاستخدام اليوم {date.today()}): ").strip()
    
    if not sale_date:
        sale_date = date.today().strftime("%Y-%m-%d")
    
    # التحقق من تحذير الربح
    if imei and db.check_device_profit_warning(imei, price):
        confirm = input("⚠️  تحذير: سعر البيع أقل من سعر الشراء! هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'نعم', 'ن']:
            print("❌ تم إلغاء العملية")
            return
    
    sale = Sale(
        customer_name=customer_name,
        customer_phone=customer_phone,
        device_name=device_name,
        imei=imei,
        price=price,
        seller=seller,
        sale_date=sale_date
    )
    
    sale_id = db.add_sale(sale)
    print(f"✅ تم إضافة عملية البيع بنجاح! رقم الفاتورة: {sale_id}")
    
    # اقتراح طباعة الفاتورة
    print_invoice = input("🖨️  هل تريد إنشاء فاتورة؟ (y/n): ").strip().lower()
    if print_invoice in ['y', 'yes', 'نعم', 'ن']:
        try:
            from utils import ReportGenerator
            report_gen = ReportGenerator()
            store_settings = db.get_store_settings()
            
            if store_settings:
                invoice_path = report_gen.generate_invoice_text(sale, store_settings)
                print(f"📄 تم إنشاء الفاتورة: {invoice_path}")
            else:
                print("❌ لم يتم العثور على إعدادات المحل")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الفاتورة: {e}")

def add_purchase(db):
    """إضافة عملية شراء جديدة"""
    from models import Purchase
    
    print("\n📦 إضافة عملية شراء جديدة")
    print("-" * 30)
    
    supplier_name = input("اسم المورد: ").strip()
    if not supplier_name:
        print("❌ اسم المورد مطلوب")
        return
    
    supplier_phone = input("رقم هاتف المورد (اختياري): ").strip()
    device_name = input("اسم الجهاز: ").strip()
    if not device_name:
        print("❌ اسم الجهاز مطلوب")
        return
    
    imei = input("رقم IMEI (اختياري): ").strip()
    
    try:
        price = float(input("سعر الشراء: ").strip())
        if price <= 0:
            print("❌ السعر يجب أن يكون أكبر من صفر")
            return
    except ValueError:
        print("❌ السعر غير صحيح")
        return
    
    purchase_date = input(f"تاريخ الشراء (اتركه فارغ لاستخدام اليوم {date.today()}): ").strip()
    
    if not purchase_date:
        purchase_date = date.today().strftime("%Y-%m-%d")
    
    purchase = Purchase(
        supplier_name=supplier_name,
        supplier_phone=supplier_phone,
        device_name=device_name,
        imei=imei,
        price=price,
        purchase_date=purchase_date
    )
    
    purchase_id = db.add_purchase(purchase)
    print(f"✅ تم إضافة عملية الشراء بنجاح! رقم العملية: {purchase_id}")

def add_expense(db):
    """إضافة مصروف جديد"""
    from models import Expense
    
    print("\n💸 إضافة مصروف جديد")
    print("-" * 30)
    
    expense_type = input("نوع المصروف: ").strip()
    if not expense_type:
        print("❌ نوع المصروف مطلوب")
        return
    
    try:
        amount = float(input("المبلغ: ").strip())
        if amount <= 0:
            print("❌ المبلغ يجب أن يكون أكبر من صفر")
            return
    except ValueError:
        print("❌ المبلغ غير صحيح")
        return
    
    expense_date = input(f"تاريخ المصروف (اتركه فارغ لاستخدام اليوم {date.today()}): ").strip()
    
    if not expense_date:
        expense_date = date.today().strftime("%Y-%m-%d")
    
    notes = input("ملاحظات (اختياري): ").strip()
    
    expense = Expense(
        expense_type=expense_type,
        amount=amount,
        expense_date=expense_date,
        notes=notes
    )
    
    expense_id = db.add_expense(expense)
    print(f"✅ تم إضافة المصروف بنجاح! رقم العملية: {expense_id}")

def show_sales(db):
    """عرض المبيعات"""
    print("\n💰 قائمة المبيعات")
    print("-" * 50)
    
    search = input("البحث (اتركه فارغ لعرض الكل): ").strip()
    limit = input("عدد النتائج (اتركه فارغ لعرض 20): ").strip()
    
    try:
        limit = int(limit) if limit else 20
    except ValueError:
        limit = 20
    
    sales = db.get_sales(limit=limit, search=search if search else None)
    
    if not sales:
        print("📭 لا توجد مبيعات")
        return
    
    print(f"\n📊 عدد النتائج: {len(sales)}")
    print("-" * 80)
    
    for sale in sales:
        print(f"🆔 {sale.id} | 👤 {sale.customer_name} | 📱 {sale.device_name}")
        print(f"   💰 {sale.price:.2f} ريال | 📅 {sale.sale_date} | 👨‍💼 {sale.seller or 'غير محدد'}")
        if sale.imei:
            print(f"   📟 IMEI: {sale.imei}")
        print("-" * 80)

def show_purchases(db):
    """عرض المشتريات"""
    print("\n📦 قائمة المشتريات")
    print("-" * 50)
    
    search = input("البحث (اتركه فارغ لعرض الكل): ").strip()
    limit = input("عدد النتائج (اتركه فارغ لعرض 20): ").strip()
    
    try:
        limit = int(limit) if limit else 20
    except ValueError:
        limit = 20
    
    purchases = db.get_purchases(limit=limit, search=search if search else None)
    
    if not purchases:
        print("📭 لا توجد مشتريات")
        return
    
    print(f"\n📊 عدد النتائج: {len(purchases)}")
    print("-" * 80)
    
    for purchase in purchases:
        print(f"🆔 {purchase.id} | 🏪 {purchase.supplier_name} | 📱 {purchase.device_name}")
        print(f"   💰 {purchase.price:.2f} ريال | 📅 {purchase.purchase_date}")
        if purchase.imei:
            print(f"   📟 IMEI: {purchase.imei}")
        print("-" * 80)

def show_expenses(db):
    """عرض المصروفات"""
    print("\n💸 قائمة المصروفات")
    print("-" * 50)
    
    search = input("البحث (اتركه فارغ لعرض الكل): ").strip()
    limit = input("عدد النتائج (اتركه فارغ لعرض 20): ").strip()
    
    try:
        limit = int(limit) if limit else 20
    except ValueError:
        limit = 20
    
    expenses = db.get_expenses(limit=limit, search=search if search else None)
    
    if not expenses:
        print("📭 لا توجد مصروفات")
        return
    
    print(f"\n📊 عدد النتائج: {len(expenses)}")
    print("-" * 80)
    
    for expense in expenses:
        print(f"🆔 {expense.id} | 🏷️ {expense.expense_type}")
        print(f"   💰 {expense.amount:.2f} ريال | 📅 {expense.expense_date}")
        if expense.notes:
            print(f"   📝 {expense.notes}")
        print("-" * 80)

def show_statistics(db):
    """عرض الإحصائيات"""
    print("\n📊 الإحصائيات")
    print("-" * 30)
    
    period = input("اختر الفترة (1=اليوم، 2=هذا الشهر، 3=مخصص): ").strip()
    
    start_date = None
    end_date = None
    
    if period == "1":
        start_date = end_date = date.today().strftime("%Y-%m-%d")
        period_name = "اليوم"
    elif period == "2":
        today = date.today()
        start_date = today.replace(day=1).strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        period_name = "هذا الشهر"
    elif period == "3":
        start_date = input("تاريخ البداية (YYYY-MM-DD): ").strip()
        end_date = input("تاريخ النهاية (YYYY-MM-DD): ").strip()
        period_name = f"من {start_date} إلى {end_date}"
    else:
        period_name = "الكل"
    
    sales_summary = db.get_sales_summary(start_date, end_date)
    purchases_summary = db.get_purchases_summary(start_date, end_date)
    expenses_summary = db.get_expenses_summary(start_date, end_date)
    
    profit = sales_summary['total'] - purchases_summary['total'] - expenses_summary['total']
    
    print(f"\n📈 إحصائيات {period_name}:")
    print("=" * 50)
    print(f"💰 المبيعات: {sales_summary['count']} عملية بقيمة {sales_summary['total']:.2f} ريال")
    print(f"📦 المشتريات: {purchases_summary['count']} عملية بقيمة {purchases_summary['total']:.2f} ريال")
    print(f"💸 المصروفات: {expenses_summary['count']} عملية بقيمة {expenses_summary['total']:.2f} ريال")
    print("=" * 50)
    print(f"💎 صافي الربح: {profit:.2f} ريال")
    print("=" * 50)

def generate_report(db, report_gen):
    """إنشاء تقرير"""
    print("\n📄 إنشاء تقرير")
    print("-" * 30)
    
    report_type = input("نوع التقرير (1=مبيعات، 2=مشتريات، 3=مصروفات، 4=شامل): ").strip()
    
    start_date = input("تاريخ البداية (YYYY-MM-DD، اتركه فارغ للكل): ").strip()
    end_date = input("تاريخ النهاية (YYYY-MM-DD، اتركه فارغ للكل): ").strip()
    
    start_date = start_date if start_date else None
    end_date = end_date if end_date else None
    
    try:
        if report_type == "1":
            sales = db.get_sales(start_date=start_date, end_date=end_date)
            store_settings = db.get_store_settings()
            if sales and store_settings:
                report_path = report_gen.generate_sales_report_text(sales, store_settings, start_date, end_date)
                print(f"✅ تم إنشاء تقرير المبيعات: {report_path}")
            else:
                print("❌ لا توجد بيانات كافية")
        
        elif report_type == "4":
            sales = db.get_sales(start_date=start_date, end_date=end_date)
            purchases = db.get_purchases(start_date=start_date, end_date=end_date)
            expenses = db.get_expenses(start_date=start_date, end_date=end_date)
            
            data = {}
            if sales:
                data['المبيعات'] = sales
            if purchases:
                data['المشتريات'] = purchases
            if expenses:
                data['المصروفات'] = expenses
            
            if data:
                report_path = report_gen.generate_csv_report(data)
                print(f"✅ تم إنشاء التقرير الشامل: {report_path}")
            else:
                print("❌ لا توجد بيانات")
        
        else:
            print("❌ نوع تقرير غير مدعوم حالياً")
    
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")

def manage_settings(db):
    """إدارة إعدادات المحل"""
    print("\n⚙️ إعدادات المحل")
    print("-" * 30)
    
    settings = db.get_store_settings()
    
    if settings:
        print("الإعدادات الحالية:")
        print(f"🏪 اسم المحل: {settings.store_name}")
        print(f"📞 الهاتف: {settings.phone}")
        print(f"📍 العنوان: {settings.address}")
        print(f"📧 البريد الإلكتروني: {settings.email}")
    else:
        print("❌ لم يتم العثور على إعدادات")

if __name__ == "__main__":
    print("🚀 تشغيل برنامج حسابات معرض دبي للموبايلات...")
    print("📝 الوضع النصي المبسط")
    
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n🙏 شكراً لاستخدام البرنامج!")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
        traceback.print_exc()
