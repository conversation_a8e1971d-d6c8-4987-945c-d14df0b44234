using System;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public class InvoiceGenerator : IDisposable
    {
        private Sale sale;
        private StoreSettings storeSettings;
        private PrintDocument printDocument;
        private Font titleFont;
        private Font headerFont;
        private Font normalFont;
        private Font boldFont;
        private Brush blackBrush;
        private Brush blueBrush;
        private Pen blackPen;

        public InvoiceGenerator()
        {
            InitializeFonts();
        }

        private void InitializeFonts()
        {
            titleFont = new Font("Arial", 18, FontStyle.Bold);
            headerFont = new Font("Arial", 14, FontStyle.Bold);
            normalFont = new Font("Arial", 10, FontStyle.Regular);
            boldFont = new Font("Arial", 10, FontStyle.Bold);
            blackBrush = new SolidBrush(Color.Black);
            blueBrush = new SolidBrush(Color.FromArgb(46, 134, 171));
            blackPen = new Pen(Color.Black, 1);
        }

        public void GenerateInvoice(Sale sale, StoreSettings storeSettings)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Show print preview
            PrintPreviewDialog previewDialog = new PrintPreviewDialog();
            previewDialog.Document = printDocument;
            previewDialog.WindowState = FormWindowState.Maximized;
            previewDialog.ShowDialog();
        }

        public void PrintInvoiceDirectly(Sale sale, StoreSettings storeSettings)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Show print dialog
            PrintDialog printDialog = new PrintDialog();
            printDialog.Document = printDocument;

            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
                MessageBox.Show("تم إرسال الفاتورة للطباعة بنجاح!", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            float yPos = 50;
            float leftMargin = 50;
            float rightMargin = e.PageBounds.Width - 50;
            float centerX = e.PageBounds.Width / 2;

            // Header with store name and logo area
            DrawHeader(g, ref yPos, leftMargin, rightMargin, centerX);

            // Invoice details
            DrawInvoiceDetails(g, ref yPos, leftMargin, rightMargin);

            // Customer information
            DrawCustomerInfo(g, ref yPos, leftMargin, rightMargin);

            // Item details
            DrawItemDetails(g, ref yPos, leftMargin, rightMargin);

            // Total and footer
            DrawTotalAndFooter(g, ref yPos, leftMargin, rightMargin, centerX);
        }

        private void DrawHeader(Graphics g, ref float yPos, float leftMargin, float rightMargin, float centerX)
        {
            // Draw logo if available
            if (!string.IsNullOrEmpty(storeSettings.LogoPath) && System.IO.File.Exists(storeSettings.LogoPath))
            {
                try
                {
                    using (var logoImage = Image.FromFile(storeSettings.LogoPath))
                    {
                        var logoSize = new Size(80, 60);
                        var logoRect = new Rectangle((int)(centerX - logoSize.Width / 2), (int)yPos, logoSize.Width, logoSize.Height);
                        g.DrawImage(logoImage, logoRect);
                        yPos += logoSize.Height + 10;
                    }
                }
                catch
                {
                    // Logo file not found or corrupted, continue without logo
                }
            }

            // Store name in Arabic
            string storeName = storeSettings.StoreName;
            SizeF storeNameSize = g.MeasureString(storeName, titleFont);
            g.DrawString(storeName, titleFont, blueBrush, centerX - storeNameSize.Width / 2, yPos);
            yPos += storeNameSize.Height + 10;

            // Store details
            string storeDetails = $"الهاتف: {storeSettings.Phone} | العنوان: {storeSettings.Address}";
            if (!string.IsNullOrEmpty(storeSettings.Email))
                storeDetails += $" | البريد الإلكتروني: {storeSettings.Email}";

            SizeF detailsSize = g.MeasureString(storeDetails, normalFont);
            g.DrawString(storeDetails, normalFont, blackBrush, centerX - detailsSize.Width / 2, yPos);
            yPos += detailsSize.Height + 20;

            // Horizontal line
            g.DrawLine(blackPen, leftMargin, yPos, rightMargin, yPos);
            yPos += 20;

            // Invoice title
            string invoiceTitle = "فاتورة بيع";
            SizeF titleSize = g.MeasureString(invoiceTitle, headerFont);
            g.DrawString(invoiceTitle, headerFont, blueBrush, centerX - titleSize.Width / 2, yPos);
            yPos += titleSize.Height + 20;
        }

        private void DrawInvoiceDetails(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Invoice number and date
            string invoiceNumber = $"رقم الفاتورة: {sale.Id}";
            string invoiceDate = $"التاريخ: {sale.SaleDate:yyyy/MM/dd}";
            string currentTime = $"الوقت: {DateTime.Now:HH:mm}";

            g.DrawString(invoiceNumber, boldFont, blackBrush, leftMargin, yPos);
            g.DrawString(invoiceDate, boldFont, blackBrush, rightMargin - g.MeasureString(invoiceDate, boldFont).Width, yPos);
            yPos += 25;

            g.DrawString(currentTime, normalFont, blackBrush, leftMargin, yPos);
            if (!string.IsNullOrEmpty(sale.Seller))
            {
                string seller = $"البائع: {sale.Seller}";
                g.DrawString(seller, normalFont, blackBrush, rightMargin - g.MeasureString(seller, normalFont).Width, yPos);
            }
            yPos += 30;
        }

        private void DrawCustomerInfo(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Customer information box
            Rectangle customerBox = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 80);
            g.DrawRectangle(blackPen, customerBox);

            // Customer info header
            g.FillRectangle(new SolidBrush(Color.FromArgb(240, 240, 240)), 
                new Rectangle(customerBox.X, customerBox.Y, customerBox.Width, 25));
            
            string customerHeader = "معلومات العميل";
            SizeF headerSize = g.MeasureString(customerHeader, boldFont);
            g.DrawString(customerHeader, boldFont, blackBrush, 
                customerBox.X + (customerBox.Width - headerSize.Width) / 2, customerBox.Y + 5);

            // Customer details
            float customerYPos = customerBox.Y + 35;
            g.DrawString($"الاسم: {sale.CustomerName}", normalFont, blackBrush, leftMargin + 10, customerYPos);
            
            if (!string.IsNullOrEmpty(sale.CustomerPhone))
            {
                string phone = $"الهاتف: {sale.CustomerPhone}";
                g.DrawString(phone, normalFont, blackBrush, 
                    rightMargin - g.MeasureString(phone, normalFont).Width - 10, customerYPos);
            }

            yPos = customerBox.Bottom + 20;
        }

        private void DrawItemDetails(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Items table header
            Rectangle tableHeader = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 30);
            g.FillRectangle(blueBrush, tableHeader);

            // Table headers
            string[] headers = { "الوصف", "رقم IMEI", "الكمية", "السعر الإجمالي" };
            float[] columnWidths = { 200, 150, 80, 120 };
            float currentX = leftMargin + 10;

            for (int i = 0; i < headers.Length; i++)
            {
                g.DrawString(headers[i], boldFont, new SolidBrush(Color.White), currentX, yPos + 8);
                currentX += columnWidths[i];
            }

            yPos += 30;

            // Table content
            Rectangle tableRow = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 40);
            g.DrawRectangle(blackPen, tableRow);

            currentX = leftMargin + 10;
            float rowYPos = yPos + 12;

            // Device name
            g.DrawString(sale.DeviceName, normalFont, blackBrush, currentX, rowYPos);
            currentX += columnWidths[0];

            // IMEI
            string imei = string.IsNullOrEmpty(sale.IMEI) ? "غير محدد" : sale.IMEI;
            g.DrawString(imei, normalFont, blackBrush, currentX, rowYPos);
            currentX += columnWidths[1];

            // Quantity
            g.DrawString("1", normalFont, blackBrush, currentX + 20, rowYPos);
            currentX += columnWidths[2];

            // Price
            string priceText = $"{sale.Price:N0} {storeSettings.CurrencySymbol}";
            g.DrawString(priceText, boldFont, blackBrush, currentX, rowYPos);

            yPos += 40;

            // Draw table borders
            g.DrawRectangle(blackPen, new Rectangle((int)leftMargin, (int)(yPos - 70), (int)(rightMargin - leftMargin), 70));
            
            // Vertical lines for columns
            currentX = leftMargin;
            for (int i = 0; i < columnWidths.Length - 1; i++)
            {
                currentX += columnWidths[i];
                g.DrawLine(blackPen, currentX, yPos - 70, currentX, yPos);
            }

            yPos += 20;
        }

        private void DrawTotalAndFooter(Graphics g, ref float yPos, float leftMargin, float rightMargin, float centerX)
        {
            // Total section
            Rectangle totalBox = new Rectangle((int)(rightMargin - 250), (int)yPos, 200, 60);
            g.DrawRectangle(blackPen, totalBox);
            g.FillRectangle(new SolidBrush(Color.FromArgb(250, 250, 250)), totalBox);

            string totalLabel = "المجموع الإجمالي:";
            string totalAmount = $"{sale.Price:N0} {storeSettings.CurrencySymbol}";

            g.DrawString(totalLabel, boldFont, blackBrush, totalBox.X + 10, totalBox.Y + 10);
            g.DrawString(totalAmount, titleFont, blueBrush, totalBox.X + 10, totalBox.Y + 30);

            yPos += 100;

            // Footer
            string footerText = "شكراً لتعاملكم معنا";
            SizeF footerSize = g.MeasureString(footerText, headerFont);
            g.DrawString(footerText, headerFont, blueBrush, centerX - footerSize.Width / 2, yPos);
            yPos += 30;

            string printTime = $"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}";
            SizeF printTimeSize = g.MeasureString(printTime, normalFont);
            g.DrawString(printTime, normalFont, blackBrush, centerX - printTimeSize.Width / 2, yPos);

            // QR Code placeholder (can be implemented later)
            yPos += 40;
            Rectangle qrBox = new Rectangle((int)(centerX - 50), (int)yPos, 100, 100);
            g.DrawRectangle(blackPen, qrBox);
            string qrText = "QR Code";
            SizeF qrTextSize = g.MeasureString(qrText, normalFont);
            g.DrawString(qrText, normalFont, blackBrush, 
                qrBox.X + (qrBox.Width - qrTextSize.Width) / 2, 
                qrBox.Y + (qrBox.Height - qrTextSize.Height) / 2);
        }

        public void SaveAsPDF(Sale sale, StoreSettings storeSettings, string filePath)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Print to PDF (requires additional PDF library)
            // For now, we'll create a text-based invoice
            CreateTextInvoice(filePath);
        }

        private void CreateTextInvoice(string filePath)
        {
            string invoiceContent = $@"
{'='*60}
{storeSettings.StoreName}
{'='*60}

الهاتف: {storeSettings.Phone}
العنوان: {storeSettings.Address}
البريد الإلكتروني: {storeSettings.Email ?? "غير محدد"}

{'='*60}
فاتورة بيع
{'='*60}

رقم الفاتورة: {sale.Id}
تاريخ البيع: {sale.SaleDate:yyyy/MM/dd}
وقت الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}
البائع: {sale.Seller ?? "غير محدد"}

{'='*60}
معلومات العميل
{'='*60}

الاسم: {sale.CustomerName}
رقم الهاتف: {sale.CustomerPhone ?? "غير محدد"}

{'='*60}
تفاصيل المنتج
{'='*60}

اسم الجهاز: {sale.DeviceName}
رقم IMEI: {sale.IMEI ?? "غير محدد"}
الكمية: 1
السعر: {sale.Price:N0} {storeSettings.CurrencySymbol}

{'='*60}
المجموع الإجمالي: {sale.Price:N0} {storeSettings.CurrencySymbol}
{'='*60}

شكراً لتعاملكم معنا
نتطلع لخدمتكم مرة أخرى

تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}
";

            File.WriteAllText(filePath, invoiceContent, System.Text.Encoding.UTF8);
        }

        public void Dispose()
        {
            titleFont?.Dispose();
            headerFont?.Dispose();
            normalFont?.Dispose();
            boldFont?.Dispose();
            blackBrush?.Dispose();
            blueBrush?.Dispose();
            blackPen?.Dispose();
            printDocument?.Dispose();
        }
    }
}
