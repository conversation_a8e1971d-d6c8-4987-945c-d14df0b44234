using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.IO;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public class InvoiceGenerator : IDisposable
    {
        private Sale sale;
        private StoreSettings storeSettings;
        private PrintDocument printDocument;
        private Font titleFont;
        private Font headerFont;
        private Font normalFont;
        private Font boldFont;
        private Brush blackBrush;
        private Brush blueBrush;
        private Pen blackPen;

        public InvoiceGenerator()
        {
            InitializeFonts();
        }

        private void InitializeFonts()
        {
            titleFont = new Font("Arial", 22, FontStyle.Bold);
            headerFont = new Font("Arial", 16, FontStyle.Bold);
            normalFont = new Font("Arial", 11, FontStyle.Regular);
            boldFont = new Font("Arial", 11, FontStyle.Bold);
            blackBrush = new SolidBrush(Color.Black);
            blueBrush = new SolidBrush(Color.FromArgb(46, 134, 171));
            blackPen = new Pen(Color.FromArgb(46, 134, 171), 2);
        }

        public void GenerateInvoice(Sale sale, StoreSettings storeSettings)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Show print preview
            PrintPreviewDialog previewDialog = new PrintPreviewDialog();
            previewDialog.Document = printDocument;
            previewDialog.WindowState = FormWindowState.Maximized;
            previewDialog.ShowDialog();
        }

        public void PrintInvoiceDirectly(Sale sale, StoreSettings storeSettings)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Show print dialog
            PrintDialog printDialog = new PrintDialog();
            printDialog.Document = printDocument;

            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
                MessageBox.Show("تم إرسال الفاتورة للطباعة بنجاح!", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            float yPos = 50;
            float leftMargin = 50;
            float rightMargin = e.PageBounds.Width - 50;
            float centerX = e.PageBounds.Width / 2;

            // Header with store name and logo area
            DrawHeader(g, ref yPos, leftMargin, rightMargin, centerX);

            // Invoice details
            DrawInvoiceDetails(g, ref yPos, leftMargin, rightMargin);

            // Customer information
            DrawCustomerInfo(g, ref yPos, leftMargin, rightMargin);

            // Item details
            DrawItemDetails(g, ref yPos, leftMargin, rightMargin);

            // Total and footer
            DrawTotalAndFooter(g, ref yPos, leftMargin, rightMargin, centerX);
        }

        private void DrawHeader(Graphics g, ref float yPos, float leftMargin, float rightMargin, float centerX)
        {
            // Draw decorative header background
            var headerRect = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 120);
            var headerBrush = new LinearGradientBrush(headerRect,
                Color.FromArgb(46, 134, 171), Color.FromArgb(241, 143, 1),
                LinearGradientMode.Horizontal);
            g.FillRectangle(headerBrush, headerRect);

            // Draw border around header
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 3), headerRect);

            float headerYPos = yPos + 15;

            // Draw logo if available
            if (!string.IsNullOrEmpty(storeSettings.LogoPath) && System.IO.File.Exists(storeSettings.LogoPath))
            {
                try
                {
                    using (var logoImage = Image.FromFile(storeSettings.LogoPath))
                    {
                        var logoSize = new Size(100, 75);
                        var logoRect = new Rectangle((int)leftMargin + 20, (int)headerYPos, logoSize.Width, logoSize.Height);

                        // Draw white background for logo
                        g.FillRectangle(Brushes.White, new Rectangle(logoRect.X - 5, logoRect.Y - 5, logoRect.Width + 10, logoRect.Height + 10));
                        g.DrawRectangle(new Pen(Color.White, 2), new Rectangle(logoRect.X - 5, logoRect.Y - 5, logoRect.Width + 10, logoRect.Height + 10));

                        g.DrawImage(logoImage, logoRect);
                    }
                }
                catch
                {
                    // Logo file not found or corrupted, continue without logo
                }
            }

            // Store name in Arabic with white text
            string storeName = storeSettings.StoreName;
            SizeF storeNameSize = g.MeasureString(storeName, titleFont);
            g.DrawString(storeName, titleFont, Brushes.White, centerX - storeNameSize.Width / 2, headerYPos + 10);

            // Store details with white text
            string storeDetails = $"📞 {storeSettings.Phone} | 📍 {storeSettings.Address}";
            if (!string.IsNullOrEmpty(storeSettings.Email))
                storeDetails += $" | 📧 {storeSettings.Email}";

            SizeF detailsSize = g.MeasureString(storeDetails, normalFont);
            g.DrawString(storeDetails, normalFont, Brushes.White, centerX - detailsSize.Width / 2, headerYPos + 45);

            // Invoice title with decorative background
            string invoiceTitle = "🧾 فاتورة بيع 🧾";
            SizeF titleSize = g.MeasureString(invoiceTitle, headerFont);
            var titleRect = new Rectangle((int)(centerX - titleSize.Width / 2 - 10), (int)(headerYPos + 75), (int)(titleSize.Width + 20), (int)(titleSize.Height + 10));
            g.FillRectangle(Brushes.White, titleRect);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 2), titleRect);
            g.DrawString(invoiceTitle, headerFont, blueBrush, centerX - titleSize.Width / 2, headerYPos + 80);

            yPos = headerRect.Bottom + 25;
            headerBrush.Dispose();
        }

        private void DrawInvoiceDetails(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Create info boxes
            var leftBox = new Rectangle((int)leftMargin, (int)yPos, 250, 80);
            var rightBox = new Rectangle((int)(rightMargin - 250), (int)yPos, 250, 80);

            // Left box - Invoice details
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), leftBox);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 2), leftBox);

            // Left box header
            var leftHeaderRect = new Rectangle(leftBox.X, leftBox.Y, leftBox.Width, 25);
            g.FillRectangle(blueBrush, leftHeaderRect);
            g.DrawString("📋 تفاصيل الفاتورة", boldFont, Brushes.White, leftBox.X + 10, leftBox.Y + 5);

            // Left box content
            string invoiceNumber = $"🔢 رقم الفاتورة: {sale.Id:D6}";
            string invoiceDate = $"📅 التاريخ: {sale.SaleDate:yyyy/MM/dd}";
            g.DrawString(invoiceNumber, normalFont, blackBrush, leftBox.X + 10, leftBox.Y + 35);
            g.DrawString(invoiceDate, normalFont, blackBrush, leftBox.X + 10, leftBox.Y + 55);

            // Right box - Time and seller
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), rightBox);
            g.DrawRectangle(new Pen(Color.FromArgb(241, 143, 1), 2), rightBox);

            // Right box header
            var rightHeaderRect = new Rectangle(rightBox.X, rightBox.Y, rightBox.Width, 25);
            g.FillRectangle(new SolidBrush(Color.FromArgb(241, 143, 1)), rightHeaderRect);
            g.DrawString("⏰ معلومات إضافية", boldFont, Brushes.White, rightBox.X + 10, rightBox.Y + 5);

            // Right box content
            string currentTime = $"🕐 الوقت: {DateTime.Now:HH:mm}";
            string seller = $"👤 البائع: {sale.Seller ?? "غير محدد"}";
            g.DrawString(currentTime, normalFont, blackBrush, rightBox.X + 10, rightBox.Y + 35);
            g.DrawString(seller, normalFont, blackBrush, rightBox.X + 10, rightBox.Y + 55);

            yPos = Math.Max(leftBox.Bottom, rightBox.Bottom) + 25;
        }

        private void DrawCustomerInfo(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Customer information box with gradient
            Rectangle customerBox = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 100);
            var customerBrush = new LinearGradientBrush(customerBox,
                Color.FromArgb(240, 248, 255), Color.FromArgb(230, 245, 255),
                LinearGradientMode.Vertical);
            g.FillRectangle(customerBrush, customerBox);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 2), customerBox);

            // Customer info header with icon
            var headerRect = new Rectangle(customerBox.X, customerBox.Y, customerBox.Width, 30);
            g.FillRectangle(new SolidBrush(Color.FromArgb(46, 134, 171)), headerRect);

            string customerHeader = "👤 معلومات العميل";
            SizeF headerSize = g.MeasureString(customerHeader, headerFont);
            g.DrawString(customerHeader, headerFont, Brushes.White,
                customerBox.X + (customerBox.Width - headerSize.Width) / 2, customerBox.Y + 5);

            // Customer details with icons and better layout
            float customerYPos = customerBox.Y + 45;

            // Customer name
            string customerName = $"👤 الاسم: {sale.CustomerName}";
            g.DrawString(customerName, boldFont, blackBrush, leftMargin + 15, customerYPos);

            // Customer phone
            if (!string.IsNullOrEmpty(sale.CustomerPhone))
            {
                string phone = $"📱 الهاتف: {sale.CustomerPhone}";
                g.DrawString(phone, boldFont, blackBrush, leftMargin + 15, customerYPos + 25);
            }
            else
            {
                g.DrawString("📱 الهاتف: غير محدد", normalFont, new SolidBrush(Color.Gray), leftMargin + 15, customerYPos + 25);
            }

            // Add decorative corner
            var cornerPoints = new Point[] {
                new Point(customerBox.Right - 20, customerBox.Y),
                new Point(customerBox.Right, customerBox.Y),
                new Point(customerBox.Right, customerBox.Y + 20)
            };
            g.FillPolygon(new SolidBrush(Color.FromArgb(241, 143, 1)), cornerPoints);

            yPos = customerBox.Bottom + 25;
            customerBrush.Dispose();
        }

        private void DrawItemDetails(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Items table header
            Rectangle tableHeader = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 30);
            g.FillRectangle(blueBrush, tableHeader);

            // Table headers
            string[] headers = { "الوصف", "رقم IMEI", "الكمية", "السعر الإجمالي" };
            float[] columnWidths = { 200, 150, 80, 120 };
            float currentX = leftMargin + 10;

            for (int i = 0; i < headers.Length; i++)
            {
                g.DrawString(headers[i], boldFont, new SolidBrush(Color.White), currentX, yPos + 8);
                currentX += columnWidths[i];
            }

            yPos += 30;

            // Table content
            Rectangle tableRow = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 40);
            g.DrawRectangle(blackPen, tableRow);

            currentX = leftMargin + 10;
            float rowYPos = yPos + 12;

            // Device name
            g.DrawString(sale.DeviceName, normalFont, blackBrush, currentX, rowYPos);
            currentX += columnWidths[0];

            // IMEI
            string imei = string.IsNullOrEmpty(sale.IMEI) ? "غير محدد" : sale.IMEI;
            g.DrawString(imei, normalFont, blackBrush, currentX, rowYPos);
            currentX += columnWidths[1];

            // Quantity
            g.DrawString("1", normalFont, blackBrush, currentX + 20, rowYPos);
            currentX += columnWidths[2];

            // Price
            string priceText = $"{sale.Price:N0} {storeSettings.CurrencySymbol}";
            g.DrawString(priceText, boldFont, blackBrush, currentX, rowYPos);

            yPos += 40;

            // Draw table borders
            g.DrawRectangle(blackPen, new Rectangle((int)leftMargin, (int)(yPos - 70), (int)(rightMargin - leftMargin), 70));
            
            // Vertical lines for columns
            currentX = leftMargin;
            for (int i = 0; i < columnWidths.Length - 1; i++)
            {
                currentX += columnWidths[i];
                g.DrawLine(blackPen, currentX, yPos - 70, currentX, yPos);
            }

            yPos += 20;
        }

        private void DrawTotalAndFooter(Graphics g, ref float yPos, float leftMargin, float rightMargin, float centerX)
        {
            // Total section with elegant design
            Rectangle totalBox = new Rectangle((int)(rightMargin - 300), (int)yPos, 280, 100);
            var totalBrush = new LinearGradientBrush(totalBox,
                Color.FromArgb(40, 167, 69), Color.FromArgb(34, 139, 34),
                LinearGradientMode.Vertical);
            g.FillRectangle(totalBrush, totalBox);
            g.DrawRectangle(new Pen(Color.FromArgb(40, 167, 69), 3), totalBox);

            // Total header
            var totalHeaderRect = new Rectangle(totalBox.X, totalBox.Y, totalBox.Width, 35);
            g.FillRectangle(new SolidBrush(Color.FromArgb(34, 139, 34)), totalHeaderRect);
            string totalHeader = "💰 المجموع الإجمالي";
            SizeF headerSize = g.MeasureString(totalHeader, headerFont);
            g.DrawString(totalHeader, headerFont, Brushes.White,
                totalBox.X + (totalBox.Width - headerSize.Width) / 2, totalBox.Y + 8);

            // Total amount with large font
            string totalAmount = $"{sale.Price:N0} {storeSettings.CurrencySymbol}";
            SizeF amountSize = g.MeasureString(totalAmount, titleFont);
            g.DrawString(totalAmount, titleFont, Brushes.White,
                totalBox.X + (totalBox.Width - amountSize.Width) / 2, totalBox.Y + 50);

            // Add decorative elements
            var starPoints = new Point[] {
                new Point(totalBox.X + 20, totalBox.Y + 60),
                new Point(totalBox.X + 25, totalBox.Y + 70),
                new Point(totalBox.X + 35, totalBox.Y + 70),
                new Point(totalBox.X + 28, totalBox.Y + 77),
                new Point(totalBox.X + 30, totalBox.Y + 87),
                new Point(totalBox.X + 20, totalBox.Y + 82),
                new Point(totalBox.X + 10, totalBox.Y + 87),
                new Point(totalBox.X + 12, totalBox.Y + 77),
                new Point(totalBox.X + 5, totalBox.Y + 70),
                new Point(totalBox.X + 15, totalBox.Y + 70)
            };
            g.FillPolygon(new SolidBrush(Color.FromArgb(255, 215, 0)), starPoints);

            yPos += 130;

            // Decorative separator
            var separatorPen = new Pen(Color.FromArgb(241, 143, 1), 3);
            g.DrawLine(separatorPen, leftMargin + 50, yPos, rightMargin - 50, yPos);
            yPos += 20;

            // Footer with thank you message
            string footerText = "🙏 شكراً لتعاملكم معنا 🙏";
            SizeF footerSize = g.MeasureString(footerText, headerFont);
            var footerRect = new Rectangle((int)(centerX - footerSize.Width / 2 - 15), (int)yPos, (int)(footerSize.Width + 30), (int)(footerSize.Height + 15));
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), footerRect);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 2), footerRect);
            g.DrawString(footerText, headerFont, blueBrush, centerX - footerSize.Width / 2, yPos + 7);
            yPos += 50;

            // Additional footer message
            string additionalText = "نتطلع لخدمتكم مرة أخرى";
            SizeF additionalSize = g.MeasureString(additionalText, normalFont);
            g.DrawString(additionalText, normalFont, new SolidBrush(Color.FromArgb(108, 117, 125)),
                centerX - additionalSize.Width / 2, yPos);
            yPos += 30;

            // Print time with style
            string printTime = $"📅 تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}";
            SizeF printTimeSize = g.MeasureString(printTime, normalFont);
            g.DrawString(printTime, normalFont, new SolidBrush(Color.FromArgb(108, 117, 125)),
                centerX - printTimeSize.Width / 2, yPos);

            // QR Code placeholder with better design
            yPos += 50;
            Rectangle qrBox = new Rectangle((int)(centerX - 60), (int)yPos, 120, 120);
            g.FillRectangle(Brushes.White, qrBox);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 2), qrBox);

            // QR Code pattern simulation
            var qrBrush = new SolidBrush(Color.FromArgb(46, 134, 171));
            for (int i = 0; i < 10; i++)
            {
                for (int j = 0; j < 10; j++)
                {
                    if ((i + j) % 2 == 0)
                    {
                        var smallRect = new Rectangle(qrBox.X + 10 + i * 10, qrBox.Y + 10 + j * 10, 8, 8);
                        g.FillRectangle(qrBrush, smallRect);
                    }
                }
            }

            string qrText = "QR Code";
            SizeF qrTextSize = g.MeasureString(qrText, normalFont);
            g.DrawString(qrText, normalFont, Brushes.White,
                qrBox.X + (qrBox.Width - qrTextSize.Width) / 2,
                qrBox.Y + qrBox.Height + 10);

            totalBrush.Dispose();
            separatorPen.Dispose();
            qrBrush.Dispose();
        }

        public void SaveAsPDF(Sale sale, StoreSettings storeSettings, string filePath)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Print to PDF (requires additional PDF library)
            // For now, we'll create a text-based invoice
            CreateTextInvoice(filePath);
        }

        private void CreateTextInvoice(string filePath)
        {
            string invoiceContent = $@"
{'='*60}
{storeSettings.StoreName}
{'='*60}

الهاتف: {storeSettings.Phone}
العنوان: {storeSettings.Address}
البريد الإلكتروني: {storeSettings.Email ?? "غير محدد"}

{'='*60}
فاتورة بيع
{'='*60}

رقم الفاتورة: {sale.Id}
تاريخ البيع: {sale.SaleDate:yyyy/MM/dd}
وقت الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}
البائع: {sale.Seller ?? "غير محدد"}

{'='*60}
معلومات العميل
{'='*60}

الاسم: {sale.CustomerName}
رقم الهاتف: {sale.CustomerPhone ?? "غير محدد"}

{'='*60}
تفاصيل المنتج
{'='*60}

اسم الجهاز: {sale.DeviceName}
رقم IMEI: {sale.IMEI ?? "غير محدد"}
الكمية: 1
السعر: {sale.Price:N0} {storeSettings.CurrencySymbol}

{'='*60}
المجموع الإجمالي: {sale.Price:N0} {storeSettings.CurrencySymbol}
{'='*60}

شكراً لتعاملكم معنا
نتطلع لخدمتكم مرة أخرى

تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}
";

            File.WriteAllText(filePath, invoiceContent, System.Text.Encoding.UTF8);
        }

        public void Dispose()
        {
            titleFont?.Dispose();
            headerFont?.Dispose();
            normalFont?.Dispose();
            boldFont?.Dispose();
            blackBrush?.Dispose();
            blueBrush?.Dispose();
            blackPen?.Dispose();
            printDocument?.Dispose();
        }
    }
}
