using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.IO;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public class InvoiceGenerator : IDisposable
    {
        private Sale sale;
        private StoreSettings storeSettings;
        private PrintDocument printDocument;
        private Font titleFont;
        private Font headerFont;
        private Font normalFont;
        private Font boldFont;
        private Brush blackBrush;
        private Brush blueBrush;
        private Pen blackPen;

        public InvoiceGenerator()
        {
            InitializeFonts();
        }

        private void InitializeFonts()
        {
            titleFont = new Font("Arial", 16, FontStyle.Bold);
            headerFont = new Font("Arial", 12, FontStyle.Bold);
            normalFont = new Font("Arial", 9, FontStyle.Regular);
            boldFont = new Font("Arial", 9, FontStyle.Bold);
            blackBrush = new SolidBrush(Color.Black);
            blueBrush = new SolidBrush(Color.FromArgb(46, 134, 171));
            blackPen = new Pen(Color.FromArgb(46, 134, 171), 1);
        }

        public void GenerateInvoice(Sale sale, StoreSettings storeSettings)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Show print preview
            PrintPreviewDialog previewDialog = new PrintPreviewDialog();
            previewDialog.Document = printDocument;
            previewDialog.WindowState = FormWindowState.Maximized;
            previewDialog.ShowDialog();
        }

        public void PrintInvoiceDirectly(Sale sale, StoreSettings storeSettings)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Show print dialog
            PrintDialog printDialog = new PrintDialog();
            printDialog.Document = printDocument;

            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
                MessageBox.Show("تم إرسال الفاتورة للطباعة بنجاح!", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            float yPos = 50;
            float leftMargin = 50;
            float rightMargin = e.PageBounds.Width - 50;
            float centerX = e.PageBounds.Width / 2;

            // Header with store name and logo area
            DrawHeader(g, ref yPos, leftMargin, rightMargin, centerX);

            // Invoice details
            DrawInvoiceDetails(g, ref yPos, leftMargin, rightMargin);

            // Customer information
            DrawCustomerInfo(g, ref yPos, leftMargin, rightMargin);

            // Item details
            DrawItemDetails(g, ref yPos, leftMargin, rightMargin);

            // Total and footer
            DrawTotalAndFooter(g, ref yPos, leftMargin, rightMargin, centerX);
        }

        private void DrawHeader(Graphics g, ref float yPos, float leftMargin, float rightMargin, float centerX)
        {
            // Draw decorative header background (smaller)
            var headerRect = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 80);
            var headerBrush = new LinearGradientBrush(headerRect,
                Color.FromArgb(46, 134, 171), Color.FromArgb(241, 143, 1),
                LinearGradientMode.Horizontal);
            g.FillRectangle(headerBrush, headerRect);

            // Draw border around header
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 2), headerRect);

            float headerYPos = yPos + 8;

            // Draw logo if available (smaller)
            if (!string.IsNullOrEmpty(storeSettings.LogoPath) && System.IO.File.Exists(storeSettings.LogoPath))
            {
                try
                {
                    using (var logoImage = Image.FromFile(storeSettings.LogoPath))
                    {
                        var logoSize = new Size(50, 40);
                        var logoRect = new Rectangle((int)leftMargin + 10, (int)headerYPos, logoSize.Width, logoSize.Height);

                        // Draw white background for logo
                        g.FillRectangle(Brushes.White, new Rectangle(logoRect.X - 2, logoRect.Y - 2, logoRect.Width + 4, logoRect.Height + 4));
                        g.DrawRectangle(new Pen(Color.White, 1), new Rectangle(logoRect.X - 2, logoRect.Y - 2, logoRect.Width + 4, logoRect.Height + 4));

                        g.DrawImage(logoImage, logoRect);
                    }
                }
                catch
                {
                    // Logo file not found or corrupted, continue without logo
                }
            }

            // Store name in Arabic with white text (compact)
            string storeName = storeSettings.StoreName;
            SizeF storeNameSize = g.MeasureString(storeName, titleFont);
            g.DrawString(storeName, titleFont, Brushes.White, centerX - storeNameSize.Width / 2, headerYPos + 5);

            // Store details with white text (compact)
            string storeDetails = $"{storeSettings.Phone} | {storeSettings.Address}";
            SizeF detailsSize = g.MeasureString(storeDetails, normalFont);
            g.DrawString(storeDetails, normalFont, Brushes.White, centerX - detailsSize.Width / 2, headerYPos + 30);

            // Invoice title with decorative background (compact)
            string invoiceTitle = "فاتورة بيع";
            SizeF titleSize = g.MeasureString(invoiceTitle, headerFont);
            var titleRect = new Rectangle((int)(centerX - titleSize.Width / 2 - 8), (int)(headerYPos + 50), (int)(titleSize.Width + 16), (int)(titleSize.Height + 8));
            g.FillRectangle(Brushes.White, titleRect);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 1), titleRect);
            g.DrawString(invoiceTitle, headerFont, blueBrush, centerX - titleSize.Width / 2, headerYPos + 54);

            yPos = headerRect.Bottom + 15;
            headerBrush.Dispose();
        }

        private void DrawInvoiceDetails(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Create compact info boxes
            var leftBox = new Rectangle((int)leftMargin, (int)yPos, 200, 50);
            var rightBox = new Rectangle((int)(rightMargin - 200), (int)yPos, 200, 50);

            // Left box - Invoice details
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), leftBox);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 1), leftBox);

            // Left box header
            var leftHeaderRect = new Rectangle(leftBox.X, leftBox.Y, leftBox.Width, 18);
            g.FillRectangle(blueBrush, leftHeaderRect);
            g.DrawString("تفاصيل الفاتورة", boldFont, Brushes.White, leftBox.X + 5, leftBox.Y + 3);

            // Left box content
            string invoiceNumber = $"رقم: {sale.Id:D6}";
            string invoiceDate = $"التاريخ: {sale.SaleDate:MM/dd}";
            g.DrawString(invoiceNumber, normalFont, blackBrush, leftBox.X + 5, leftBox.Y + 22);
            g.DrawString(invoiceDate, normalFont, blackBrush, leftBox.X + 5, leftBox.Y + 35);

            // Right box - Time and seller
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), rightBox);
            g.DrawRectangle(new Pen(Color.FromArgb(241, 143, 1), 1), rightBox);

            // Right box header
            var rightHeaderRect = new Rectangle(rightBox.X, rightBox.Y, rightBox.Width, 18);
            g.FillRectangle(new SolidBrush(Color.FromArgb(241, 143, 1)), rightHeaderRect);
            g.DrawString("معلومات إضافية", boldFont, Brushes.White, rightBox.X + 5, rightBox.Y + 3);

            // Right box content
            string currentTime = $"الوقت: {DateTime.Now:HH:mm}";
            string seller = $"البائع: {sale.Seller ?? "غير محدد"}";
            g.DrawString(currentTime, normalFont, blackBrush, rightBox.X + 5, rightBox.Y + 22);
            g.DrawString(seller, normalFont, blackBrush, rightBox.X + 5, rightBox.Y + 35);

            yPos = Math.Max(leftBox.Bottom, rightBox.Bottom) + 15;
        }

        private void DrawCustomerInfo(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Customer information box with gradient (compact)
            Rectangle customerBox = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 60);
            var customerBrush = new LinearGradientBrush(customerBox,
                Color.FromArgb(240, 248, 255), Color.FromArgb(230, 245, 255),
                LinearGradientMode.Vertical);
            g.FillRectangle(customerBrush, customerBox);
            g.DrawRectangle(new Pen(Color.FromArgb(46, 134, 171), 1), customerBox);

            // Customer info header with icon (compact)
            var headerRect = new Rectangle(customerBox.X, customerBox.Y, customerBox.Width, 20);
            g.FillRectangle(new SolidBrush(Color.FromArgb(46, 134, 171)), headerRect);

            string customerHeader = "معلومات العميل";
            SizeF headerSize = g.MeasureString(customerHeader, boldFont);
            g.DrawString(customerHeader, boldFont, Brushes.White,
                customerBox.X + (customerBox.Width - headerSize.Width) / 2, customerBox.Y + 4);

            // Customer details with icons and better layout (compact)
            float customerYPos = customerBox.Y + 25;

            // Customer name and phone in one line
            string customerInfo = $"الاسم: {sale.CustomerName}";
            if (!string.IsNullOrEmpty(sale.CustomerPhone))
            {
                customerInfo += $" | الهاتف: {sale.CustomerPhone}";
            }
            else
            {
                customerInfo += " | الهاتف: غير محدد";
            }

            g.DrawString(customerInfo, normalFont, blackBrush, leftMargin + 10, customerYPos);

            yPos = customerBox.Bottom + 15;
            customerBrush.Dispose();
        }

        private void DrawItemDetails(Graphics g, ref float yPos, float leftMargin, float rightMargin)
        {
            // Items table header (compact)
            Rectangle tableHeader = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 20);
            g.FillRectangle(blueBrush, tableHeader);

            // Table headers (compact)
            string[] headers = { "الوصف", "IMEI", "الكمية", "السعر" };
            float[] columnWidths = { 150, 120, 60, 100 };
            float currentX = leftMargin + 5;

            for (int i = 0; i < headers.Length; i++)
            {
                g.DrawString(headers[i], normalFont, new SolidBrush(Color.White), currentX, yPos + 5);
                currentX += columnWidths[i];
            }

            yPos += 20;

            // Table content (compact)
            Rectangle tableRow = new Rectangle((int)leftMargin, (int)yPos, (int)(rightMargin - leftMargin), 25);
            g.DrawRectangle(blackPen, tableRow);

            currentX = leftMargin + 5;
            float rowYPos = yPos + 6;

            // Device name (compact)
            g.DrawString(sale.DeviceName, normalFont, blackBrush, currentX, rowYPos);
            currentX += columnWidths[0];

            // IMEI (compact)
            string imei = string.IsNullOrEmpty(sale.IMEI) ? "غير محدد" : sale.IMEI;
            if (imei != "غير محدد" && imei.Length > 8)
            {
                imei = imei.Substring(0, 8) + "...";
            }
            g.DrawString(imei, normalFont, blackBrush, currentX, rowYPos);
            currentX += columnWidths[1];

            // Quantity (compact)
            g.DrawString("1", normalFont, blackBrush, currentX + 15, rowYPos);
            currentX += columnWidths[2];

            // Price (compact)
            string priceText = $"{sale.Price:N0} {storeSettings.CurrencySymbol}";
            g.DrawString(priceText, boldFont, blackBrush, currentX, rowYPos);

            yPos += 25;

            // Draw table borders (compact)
            g.DrawRectangle(blackPen, new Rectangle((int)leftMargin, (int)(yPos - 45), (int)(rightMargin - leftMargin), 45));

            // Vertical lines for columns
            currentX = leftMargin;
            for (int i = 0; i < columnWidths.Length - 1; i++)
            {
                currentX += columnWidths[i];
                g.DrawLine(blackPen, currentX, yPos - 45, currentX, yPos);
            }

            yPos += 10;
        }

        private void DrawTotalAndFooter(Graphics g, ref float yPos, float leftMargin, float rightMargin, float centerX)
        {
            // Total section (compact)
            Rectangle totalBox = new Rectangle((int)(rightMargin - 200), (int)yPos, 180, 50);
            var totalBrush = new LinearGradientBrush(totalBox,
                Color.FromArgb(40, 167, 69), Color.FromArgb(34, 139, 34),
                LinearGradientMode.Vertical);
            g.FillRectangle(totalBrush, totalBox);
            g.DrawRectangle(new Pen(Color.FromArgb(40, 167, 69), 2), totalBox);

            // Total header (compact)
            var totalHeaderRect = new Rectangle(totalBox.X, totalBox.Y, totalBox.Width, 20);
            g.FillRectangle(new SolidBrush(Color.FromArgb(34, 139, 34)), totalHeaderRect);
            string totalHeader = "المجموع الإجمالي";
            SizeF headerSize = g.MeasureString(totalHeader, boldFont);
            g.DrawString(totalHeader, boldFont, Brushes.White,
                totalBox.X + (totalBox.Width - headerSize.Width) / 2, totalBox.Y + 4);

            // Total amount (compact)
            string totalAmount = $"{sale.Price:N0} {storeSettings.CurrencySymbol}";
            SizeF amountSize = g.MeasureString(totalAmount, headerFont);
            g.DrawString(totalAmount, headerFont, Brushes.White,
                totalBox.X + (totalBox.Width - amountSize.Width) / 2, totalBox.Y + 25);

            yPos += 70;

            // Footer (compact)
            string footerText = "شكراً لتعاملكم معنا";
            SizeF footerSize = g.MeasureString(footerText, boldFont);
            g.DrawString(footerText, boldFont, blueBrush, centerX - footerSize.Width / 2, yPos);
            yPos += 20;

            // Print time (compact)
            string printTime = $"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}";
            SizeF printTimeSize = g.MeasureString(printTime, normalFont);
            g.DrawString(printTime, normalFont, new SolidBrush(Color.FromArgb(108, 117, 125)),
                centerX - printTimeSize.Width / 2, yPos);

            totalBrush.Dispose();
        }

        public void SaveAsPDF(Sale sale, StoreSettings storeSettings, string filePath)
        {
            this.sale = sale;
            this.storeSettings = storeSettings;

            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;

            // Print to PDF (requires additional PDF library)
            // For now, we'll create a text-based invoice
            CreateTextInvoice(filePath);
        }

        private void CreateTextInvoice(string filePath)
        {
            string invoiceContent = $@"
{'='*60}
{storeSettings.StoreName}
{'='*60}

الهاتف: {storeSettings.Phone}
العنوان: {storeSettings.Address}
البريد الإلكتروني: {storeSettings.Email ?? "غير محدد"}

{'='*60}
فاتورة بيع
{'='*60}

رقم الفاتورة: {sale.Id}
تاريخ البيع: {sale.SaleDate:yyyy/MM/dd}
وقت الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}
البائع: {sale.Seller ?? "غير محدد"}

{'='*60}
معلومات العميل
{'='*60}

الاسم: {sale.CustomerName}
رقم الهاتف: {sale.CustomerPhone ?? "غير محدد"}

{'='*60}
تفاصيل المنتج
{'='*60}

اسم الجهاز: {sale.DeviceName}
رقم IMEI: {sale.IMEI ?? "غير محدد"}
الكمية: 1
السعر: {sale.Price:N0} {storeSettings.CurrencySymbol}

{'='*60}
المجموع الإجمالي: {sale.Price:N0} {storeSettings.CurrencySymbol}
{'='*60}

شكراً لتعاملكم معنا
نتطلع لخدمتكم مرة أخرى

تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}
";

            File.WriteAllText(filePath, invoiceContent, System.Text.Encoding.UTF8);
        }

        public void Dispose()
        {
            titleFont?.Dispose();
            headerFont?.Dispose();
            normalFont?.Dispose();
            boldFont?.Dispose();
            blackBrush?.Dispose();
            blueBrush?.Dispose();
            blackPen?.Dispose();
            printDocument?.Dispose();
        }
    }
}
