using System;
using System.Drawing;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class MainForm : Form
    {
        private Panel sidePanel;
        private Panel contentPanel;
        private Panel headerPanel;
        private Label titleLabel;
        private Button homeButton;
        private Button salesButton;
        private Button purchasesButton;
        private Button expensesButton;
        private Button returnsButton;
        private Button inventoryButton;
        private Button reportsButton;
        private Button settingsButton;

        public MainForm()
        {
            InitializeComponent();
            InitializeAdvancedFeatures();
            LoadDashboard();
        }

        private void InitializeAdvancedFeatures()
        {
            // Initialize auto backup
            AutoBackupManager.StartAutoBackup();

            // Initialize smart notifications
            SmartNotificationManager.Initialize();
            SmartNotificationManager.ShowInventoryForm += () => LoadInventoryForm();
            SmartNotificationManager.ShowSalesForm += () => LoadSalesForm();
            SmartNotificationManager.ShowReturnsForm += () => LoadReturnsForm();

            // Initialize keyboard shortcuts
            KeyboardShortcutManager.RegisterShortcuts(this);
            KeyboardShortcutManager.OnNavigateToHome += () => LoadDashboard();
            KeyboardShortcutManager.OnNavigateToSales += () => LoadSalesForm();
            KeyboardShortcutManager.OnNavigateToPurchases += () => LoadPurchasesForm();
            KeyboardShortcutManager.OnNavigateToExpenses += () => LoadExpensesForm();
            KeyboardShortcutManager.OnNavigateToReturns += () => LoadReturnsForm();
            KeyboardShortcutManager.OnNavigateToInventory += () => LoadInventoryForm();
            KeyboardShortcutManager.OnNavigateToReports += () => LoadReportsForm();
            KeyboardShortcutManager.OnNavigateToSettings += () => LoadSettingsForm();
        }

        private void InitializeComponent()
        {
            this.Text = "معرض دبي للموبايلات - نظام الإدارة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.BackColor = Color.FromArgb(240, 240, 240);

            // Create main layout
            CreateSidebar();
            CreateContentArea();

            // Load dashboard by default
            LoadDashboard();
        }

        private void CreateSidebar()
        {
            // Sidebar Panel
            sidePanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 200,
                BackColor = Color.FromArgb(64, 64, 64),
                Padding = new Padding(5)
            };

            var buttonHeight = 40;
            var buttonMargin = 5;
            var currentY = 20;

            // Home Button
            homeButton = CreateSideButton("الرئيسية", currentY);
            homeButton.Click += (s, e) => { SetActiveButton(homeButton); LoadDashboard(); };
            currentY += buttonHeight + buttonMargin;

            // Sales Button
            salesButton = CreateSideButton("المبيعات", currentY);
            salesButton.Click += (s, e) => { SetActiveButton(salesButton); LoadSalesForm(); };
            currentY += buttonHeight + buttonMargin;

            // Purchases Button
            purchasesButton = CreateSideButton("المشتريات", currentY);
            purchasesButton.Click += (s, e) => { SetActiveButton(purchasesButton); LoadPurchasesForm(); };
            currentY += buttonHeight + buttonMargin;

            // Expenses Button
            expensesButton = CreateSideButton("المصروفات", currentY);
            expensesButton.Click += (s, e) => { SetActiveButton(expensesButton); LoadExpensesForm(); };
            currentY += buttonHeight + buttonMargin;

            // Returns Button
            returnsButton = CreateSideButton("المرتجعات", currentY);
            returnsButton.Click += (s, e) => { SetActiveButton(returnsButton); LoadReturnsForm(); };
            currentY += buttonHeight + buttonMargin;

            // Inventory Button
            inventoryButton = CreateSideButton("المخزون", currentY);
            inventoryButton.Click += (s, e) => { SetActiveButton(inventoryButton); LoadInventoryForm(); };
            currentY += buttonHeight + buttonMargin;

            // Reports Button
            reportsButton = CreateSideButton("التقارير", currentY);
            reportsButton.Click += (s, e) => { SetActiveButton(reportsButton); LoadReportsForm(); };
            currentY += buttonHeight + buttonMargin;

            // Settings Button
            settingsButton = CreateSideButton("الإعدادات", currentY);
            settingsButton.Click += (s, e) => { SetActiveButton(settingsButton); LoadSettingsForm(); };

            sidePanel.Controls.AddRange(new Control[] {
                homeButton, salesButton, purchasesButton, expensesButton, returnsButton,
                inventoryButton, reportsButton, settingsButton
            });

            this.Controls.Add(sidePanel);

            // Set home as active by default
            SetActiveButton(homeButton);
        }

        private void CreateContentArea()
        {
            // Content Panel
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            this.Controls.Add(contentPanel);
        }

        private Button CreateSideButton(string text, int y)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(190, 40),
                Location = new Point(5, y),
                BackColor = Color.FromArgb(80, 80, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = Color.FromArgb(100, 100, 100);

            return button;
        }

        private void LoadDashboard()
        {
            contentPanel.Controls.Clear();

            var dashboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Welcome Label
            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام إدارة معرض دبي للموبايلات",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Add panels to dashboard
            var statisticsPanel = CreateStatisticsPanel();
            var activitiesPanel = CreateRecentActivitiesPanel();

            dashboardPanel.Controls.AddRange(new Control[] {
                welcomeLabel, statisticsPanel, activitiesPanel
            });

            contentPanel.Controls.Add(dashboardPanel);
        }

        private Panel CreateStatisticsPanel()
        {
            var panel = new Panel
            {
                Size = new Size(900, 200),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(20, 60)
            };

            var titleLabel = new Label
            {
                Text = "إحصائيات اليوم",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true
            };

            // Get today's statistics
            var stats = DatabaseManager.GetStatistics(DateTime.Today, DateTime.Today);

            var salesCard = CreateStatCard("المبيعات", stats.SalesCount.ToString(),
                $"{stats.SalesTotal:N0} د.ع", Color.FromArgb(40, 167, 69), 20, 50);

            var purchasesCard = CreateStatCard("المشتريات", stats.PurchasesCount.ToString(),
                $"{stats.PurchasesTotal:N0} د.ع", Color.FromArgb(255, 193, 7), 200, 50);

            var expensesCard = CreateStatCard("المصروفات", stats.ExpensesCount.ToString(),
                $"{stats.ExpensesTotal:N0} د.ع", Color.FromArgb(220, 53, 69), 380, 50);

            var returnsCard = CreateStatCard("المرتجعات", stats.ReturnsCount.ToString(),
                $"{stats.ReturnsTotal:N0} د.ع", Color.FromArgb(255, 87, 34), 560, 50);

            var profitCard = CreateStatCard("صافي الربح", "",
                $"{stats.NetProfit:N0} د.ع", Color.FromArgb(23, 162, 184), 740, 50);

            panel.Controls.AddRange(new Control[] { titleLabel, salesCard, purchasesCard, expensesCard, returnsCard, profitCard });

            return panel;
        }

        private Panel CreateStatCard(string title, string count, string amount, Color color, int x, int y)
        {
            var card = new Panel
            {
                Size = new Size(160, 120),
                Location = new Point(x, y),
                BackColor = color
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                AutoSize = true
            };

            var countLabel = new Label
            {
                Text = count,
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                AutoSize = true
            };

            var amountLabel = new Label
            {
                Text = amount,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(10, 85),
                AutoSize = true
            };

            card.Controls.AddRange(new Control[] { titleLabel, countLabel, amountLabel });

            return card;
        }



        private Panel CreateRecentActivitiesPanel()
        {
            var panel = new Panel
            {
                Size = new Size(900, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(20, 280)
            };

            var titleLabel = new Label
            {
                Text = "الأنشطة الأخيرة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true
            };

            // Create DataGridView for recent sales
            var dataGridView = new DataGridView
            {
                Location = new Point(10, 40),
                Size = new Size(870, 240),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // Load recent sales
            var recentSales = DatabaseManager.GetSales(10);
            dataGridView.DataSource = recentSales;

            // Configure columns
            if (dataGridView.Columns.Count > 0)
            {
                dataGridView.Columns["Id"].HeaderText = "الرقم";
                dataGridView.Columns["CustomerName"].HeaderText = "العميل";
                dataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                dataGridView.Columns["Price"].HeaderText = "السعر";
                dataGridView.Columns["SaleDate"].HeaderText = "التاريخ";

                // Hide unnecessary columns
                dataGridView.Columns["CustomerPhone"].Visible = false;
                dataGridView.Columns["IMEI"].Visible = false;
                dataGridView.Columns["Seller"].Visible = false;
                dataGridView.Columns["CreatedAt"].Visible = false;
            }

            panel.Controls.AddRange(new Control[] { titleLabel, dataGridView });

            return panel;
        }

        private void LoadSalesForm()
        {
            contentPanel.Controls.Clear();
            var salesForm = new SalesUserControl();
            salesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(salesForm);
        }

        private void LoadPurchasesForm()
        {
            contentPanel.Controls.Clear();
            var purchasesForm = new PurchasesUserControl();
            purchasesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(purchasesForm);
        }

        private void LoadExpensesForm()
        {
            contentPanel.Controls.Clear();
            var expensesForm = new ExpensesUserControl();
            expensesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(expensesForm);
        }

        private void LoadReturnsForm()
        {
            contentPanel.Controls.Clear();
            var returnsForm = new ReturnsUserControl();
            returnsForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(returnsForm);
        }

        private void LoadInventoryForm()
        {
            contentPanel.Controls.Clear();
            var inventoryForm = new InventoryUserControl();
            inventoryForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(inventoryForm);
        }

        private void LoadReportsForm()
        {
            contentPanel.Controls.Clear();
            var reportsForm = new ReportsUserControl();
            reportsForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(reportsForm);
        }

        private void LoadSettingsForm()
        {
            contentPanel.Controls.Clear();
            var settingsForm = new SettingsUserControl();
            settingsForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(settingsForm);
        }

        private void SetActiveButton(Button activeButton)
        {
            // Reset all buttons
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button button && button != activeButton)
                {
                    button.BackColor = Color.FromArgb(52, 58, 64);
                    button.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
                }
            }

            // Set active button
            activeButton.BackColor = Color.FromArgb(0, 123, 255);
            activeButton.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
        }

        private void HeaderPanel_Paint(object sender, PaintEventArgs e)
        {
            // Create gradient background for header
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                headerPanel.ClientRectangle,
                Color.FromArgb(25, 118, 210),
                Color.FromArgb(13, 71, 161),
                System.Drawing.Drawing2D.LinearGradientMode.Horizontal))
            {
                e.Graphics.FillRectangle(brush, headerPanel.ClientRectangle);
            }
        }

        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // Add subtle border to side panel
            using (var pen = new Pen(Color.FromArgb(73, 80, 87), 1))
            {
                e.Graphics.DrawLine(pen, 0, 0, 0, sidePanel.Height);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Clean up advanced features
                AutoBackupManager.StopAutoBackup();
                SmartNotificationManager.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
