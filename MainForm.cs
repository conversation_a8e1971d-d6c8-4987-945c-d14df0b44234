using System;
using System.Drawing;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class MainForm : Form
    {
        private Panel sidePanel;
        private Panel contentPanel;
        private Panel headerPanel;
        private Label titleLabel;
        private Button homeButton;
        private Button salesButton;
        private Button purchasesButton;
        private Button expensesButton;
        private Button returnsButton;
        private Button inventoryButton;
        private Button reportsButton;
        private Button settingsButton;

        public MainForm()
        {
            InitializeComponent();
            InitializeAdvancedFeatures();
            LoadDashboard();
        }

        private void InitializeAdvancedFeatures()
        {
            // Initialize auto backup
            AutoBackupManager.StartAutoBackup();

            // Initialize smart notifications
            SmartNotificationManager.Initialize();
            SmartNotificationManager.ShowInventoryForm += () => LoadInventoryForm();
            SmartNotificationManager.ShowSalesForm += () => LoadSalesForm();
            SmartNotificationManager.ShowReturnsForm += () => LoadReturnsForm();

            // Initialize keyboard shortcuts
            KeyboardShortcutManager.RegisterShortcuts(this);
            KeyboardShortcutManager.OnNavigateToHome += () => LoadDashboard();
            KeyboardShortcutManager.OnNavigateToSales += () => LoadSalesForm();
            KeyboardShortcutManager.OnNavigateToPurchases += () => LoadPurchasesForm();
            KeyboardShortcutManager.OnNavigateToExpenses += () => LoadExpensesForm();
            KeyboardShortcutManager.OnNavigateToReturns += () => LoadReturnsForm();
            KeyboardShortcutManager.OnNavigateToInventory += () => LoadInventoryForm();
            KeyboardShortcutManager.OnNavigateToReports += () => LoadReportsForm();
            KeyboardShortcutManager.OnNavigateToSettings += () => LoadSettingsForm();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "معرض دبي للموبايلات - نظام إدارة احترافي";
            this.Size = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 248);
            this.WindowState = FormWindowState.Maximized;

            // Header Panel with gradient effect
            headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = Color.FromArgb(25, 118, 210)
            };
            headerPanel.Paint += HeaderPanel_Paint;

            // Logo placeholder
            var logoLabel = new Label
            {
                Text = "📱",
                Font = new Font("Segoe UI Emoji", 24F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 25),
                Size = new Size(50, 50),
                TextAlign = ContentAlignment.MiddleCenter
            };

            titleLabel = new Label
            {
                Text = "معرض دبي للموبايلات",
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(80, 15),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "نظام إدارة احترافي ومتطور",
                Font = new Font("Segoe UI", 11F, FontStyle.Regular),
                ForeColor = Color.FromArgb(200, 230, 255),
                Location = new Point(80, 45),
                AutoSize = true
            };

            // Current time label
            var timeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(200, 230, 255),
                Location = new Point(80, 70),
                AutoSize = true
            };

            headerPanel.Controls.AddRange(new Control[] { logoLabel, titleLabel, subtitleLabel, timeLabel });

            // Side Panel with modern design
            sidePanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 250,
                BackColor = Color.FromArgb(33, 37, 41),
                Padding = new Padding(10)
            };
            sidePanel.Paint += SidePanel_Paint;

            CreateSideButtons();

            // Content Panel with modern styling
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 250, 252),
                Padding = new Padding(20)
            };

            // Add controls to form
            this.Controls.Add(contentPanel);
            this.Controls.Add(sidePanel);
            this.Controls.Add(headerPanel);
        }

        private void CreateSideButtons()
        {
            var buttonHeight = 55;
            var buttonMargin = 8;
            var currentY = 30;

            // Add title for navigation
            var navTitle = new Label
            {
                Text = "القوائم الرئيسية",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(173, 181, 189),
                Location = new Point(15, 10),
                AutoSize = true
            };
            sidePanel.Controls.Add(navTitle);

            // Home Button
            homeButton = CreateSideButton("🏠  الرئيسية", currentY);
            homeButton.Click += (s, e) => { SetActiveButton(homeButton); LoadDashboard(); };
            currentY += buttonHeight + buttonMargin;

            // Sales Button
            salesButton = CreateSideButton("💰  المبيعات", currentY);
            salesButton.Click += (s, e) => { SetActiveButton(salesButton); LoadSalesForm(); };
            currentY += buttonHeight + buttonMargin;

            // Purchases Button
            purchasesButton = CreateSideButton("📦  المشتريات", currentY);
            purchasesButton.Click += (s, e) => { SetActiveButton(purchasesButton); LoadPurchasesForm(); };
            currentY += buttonHeight + buttonMargin;

            // Expenses Button
            expensesButton = CreateSideButton("💸  المصروفات", currentY);
            expensesButton.Click += (s, e) => { SetActiveButton(expensesButton); LoadExpensesForm(); };
            currentY += buttonHeight + buttonMargin;

            // Returns Button
            returnsButton = CreateSideButton("↩️  المرتجعات", currentY);
            returnsButton.Click += (s, e) => { SetActiveButton(returnsButton); LoadReturnsForm(); };
            currentY += buttonHeight + buttonMargin;

            // Inventory Button
            inventoryButton = CreateSideButton("📋  المخزون", currentY);
            inventoryButton.Click += (s, e) => { SetActiveButton(inventoryButton); LoadInventoryForm(); };
            currentY += buttonHeight + buttonMargin;

            // Reports Button
            reportsButton = CreateSideButton("📊  التقارير", currentY);
            reportsButton.Click += (s, e) => { SetActiveButton(reportsButton); LoadReportsForm(); };
            currentY += buttonHeight + buttonMargin;

            // Settings Button
            settingsButton = CreateSideButton("⚙️  الإعدادات", currentY);
            settingsButton.Click += (s, e) => { SetActiveButton(settingsButton); LoadSettingsForm(); };

            sidePanel.Controls.AddRange(new Control[] {
                homeButton, salesButton, purchasesButton, expensesButton, returnsButton,
                inventoryButton, reportsButton, settingsButton
            });

            // Set home as active by default
            SetActiveButton(homeButton);
        }

        private Button CreateSideButton(string text, int y)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(230, 55),
                Location = new Point(10, y),
                BackColor = Color.FromArgb(52, 58, 64),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft,
                Cursor = Cursors.Hand,
                Padding = new Padding(15, 0, 0, 0),
                ImageAlign = ContentAlignment.MiddleLeft
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(0, 123, 255);
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(0, 86, 179);

            // Add hover effects
            button.MouseEnter += (s, e) => {
                button.BackColor = Color.FromArgb(0, 123, 255);
                button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = Color.FromArgb(52, 58, 64);
                button.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
            };

            return button;
        }

        private void LoadDashboard()
        {
            contentPanel.Controls.Clear();

            var dashboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 250, 252),
                AutoScroll = true
            };

            // Welcome Section
            var welcomePanel = new Panel
            {
                Size = new Size(1100, 120),
                Location = new Point(0, 0),
                BackColor = Color.White,
                Margin = new Padding(0, 0, 0, 20)
            };
            welcomePanel.Paint += (s, e) => DrawCardShadow(e.Graphics, welcomePanel.ClientRectangle);

            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في معرض دبي للموبايلات",
                Font = new Font("Segoe UI", 24F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(30, 20),
                AutoSize = true
            };

            var welcomeSubLabel = new Label
            {
                Text = "نظام إدارة احترافي ومتطور لإدارة جميع عمليات المعرض",
                Font = new Font("Segoe UI", 12F, FontStyle.Regular),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(30, 60),
                AutoSize = true
            };

            var currentTimeLabel = new Label
            {
                Text = $"التاريخ والوقت: {DateTime.Now:yyyy/MM/dd - dddd - HH:mm}",
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(30, 85),
                AutoSize = true
            };

            welcomePanel.Controls.AddRange(new Control[] { welcomeLabel, welcomeSubLabel, currentTimeLabel });

            // Statistics Panel
            var statsPanel = CreateStatisticsPanel();
            statsPanel.Location = new Point(20, 80);

            // Recent Activities Panel
            var activitiesPanel = CreateRecentActivitiesPanel();
            activitiesPanel.Location = new Point(20, 300);

            dashboardPanel.Controls.AddRange(new Control[] {
                welcomePanel, statsPanel, activitiesPanel
            });

            contentPanel.Controls.Add(dashboardPanel);
        }

        private Panel CreateStatisticsPanel()
        {
            var panel = new Panel
            {
                Size = new Size(1100, 220),
                Location = new Point(0, 140),
                BackColor = Color.Transparent
            };

            var titleLabel = new Label
            {
                Text = "📊 إحصائيات اليوم",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(0, 0),
                AutoSize = true
            };

            // Get today's statistics
            var stats = DatabaseManager.GetStatistics(DateTime.Today, DateTime.Today);

            var salesCard = CreateStatCard("المبيعات", stats.SalesCount.ToString(),
                $"{stats.SalesTotal:N0} د.إ", Color.FromArgb(40, 167, 69), 0, 40);

            var purchasesCard = CreateStatCard("المشتريات", stats.PurchasesCount.ToString(),
                $"{stats.PurchasesTotal:N0} د.إ", Color.FromArgb(255, 193, 7), 240, 40);

            var expensesCard = CreateStatCard("المصروفات", stats.ExpensesCount.ToString(),
                $"{stats.ExpensesTotal:N0} د.إ", Color.FromArgb(220, 53, 69), 480, 40);

            var returnsCard = CreateStatCard("المرتجعات", stats.ReturnsCount.ToString(),
                $"{stats.ReturnsTotal:N0} د.إ", Color.FromArgb(255, 87, 34), 720, 40);

            var profitCard = CreateStatCard("صافي الربح", "",
                $"{stats.NetProfit:N0} د.إ", Color.FromArgb(23, 162, 184), 960, 40);

            panel.Controls.AddRange(new Control[] { titleLabel, salesCard, purchasesCard, expensesCard, returnsCard, profitCard });

            return panel;
        }

        private Panel CreateStatCard(string title, string count, string amount, Color color, int x, int y)
        {
            var card = new Panel
            {
                Size = new Size(220, 140),
                Location = new Point(x, y),
                BackColor = Color.White,
                Margin = new Padding(10)
            };
            card.Paint += (s, e) => {
                DrawCardShadow(e.Graphics, card.ClientRectangle);
                // Draw colored top border
                using (var brush = new SolidBrush(color))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, card.Width, 4);
                }
            };

            // Icon
            var iconLabel = new Label
            {
                Text = GetIconForTitle(title),
                Font = new Font("Segoe UI Emoji", 24F, FontStyle.Regular),
                ForeColor = color,
                Location = new Point(20, 20),
                Size = new Size(40, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(70, 20),
                AutoSize = true
            };

            var countLabel = new Label
            {
                Text = count,
                Font = new Font("Segoe UI", 28F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 50),
                AutoSize = true
            };

            var amountLabel = new Label
            {
                Text = amount,
                Font = new Font("Segoe UI", 12F, FontStyle.Regular),
                ForeColor = color,
                Location = new Point(20, 100),
                AutoSize = true
            };

            card.Controls.AddRange(new Control[] { iconLabel, titleLabel, countLabel, amountLabel });

            return card;
        }

        private string GetIconForTitle(string title)
        {
            return title switch
            {
                "المبيعات" => "💰",
                "المشتريات" => "📦",
                "المصروفات" => "💸",
                "المرتجعات" => "↩️",
                "صافي الربح" => "📈",
                _ => "📊"
            };
        }

        private void DrawCardShadow(Graphics g, Rectangle bounds)
        {
            // Draw subtle shadow effect
            using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
            {
                g.FillRectangle(shadowBrush, bounds.X + 2, bounds.Y + 2, bounds.Width, bounds.Height);
            }

            // Draw white background
            using (var backgroundBrush = new SolidBrush(Color.White))
            {
                g.FillRectangle(backgroundBrush, bounds);
            }

            // Draw border
            using (var borderPen = new Pen(Color.FromArgb(222, 226, 230), 1))
            {
                g.DrawRectangle(borderPen, bounds.X, bounds.Y, bounds.Width - 1, bounds.Height - 1);
            }
        }

        private Panel CreateRecentActivitiesPanel()
        {
            var panel = new Panel
            {
                Size = new Size(1100, 350),
                Location = new Point(0, 380),
                BackColor = Color.White
            };
            panel.Paint += (s, e) => DrawCardShadow(e.Graphics, panel.ClientRectangle);

            var titleLabel = new Label
            {
                Text = "📋 الأنشطة الأخيرة",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 15),
                AutoSize = true
            };

            // Create modern DataGridView for recent sales
            var dataGridView = new DataGridView
            {
                Location = new Point(20, 50),
                Size = new Size(1060, 280),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                GridColor = Color.FromArgb(222, 226, 230),
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Style the headers
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.FromArgb(73, 80, 87);
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.ColumnHeadersHeight = 40;

            // Style the rows
            dataGridView.DefaultCellStyle.BackColor = Color.White;
            dataGridView.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.RowTemplate.Height = 35;

            // Alternating row colors
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // Load recent sales
            var recentSales = DatabaseManager.GetSales(10);
            
            dataGridView.DataSource = recentSales;

            // Configure columns
            if (dataGridView.Columns.Count > 0)
            {
                dataGridView.Columns["Id"].HeaderText = "الرقم";
                dataGridView.Columns["CustomerName"].HeaderText = "العميل";
                dataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                dataGridView.Columns["Price"].HeaderText = "السعر";
                dataGridView.Columns["SaleDate"].HeaderText = "التاريخ";
                
                // Hide unnecessary columns
                dataGridView.Columns["CustomerPhone"].Visible = false;
                dataGridView.Columns["IMEI"].Visible = false;
                dataGridView.Columns["Seller"].Visible = false;
                dataGridView.Columns["CreatedAt"].Visible = false;
            }

            panel.Controls.AddRange(new Control[] { titleLabel, dataGridView });

            return panel;
        }

        private void LoadSalesForm()
        {
            contentPanel.Controls.Clear();
            var salesForm = new SalesUserControl();
            salesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(salesForm);
        }

        private void LoadPurchasesForm()
        {
            contentPanel.Controls.Clear();
            var purchasesForm = new PurchasesUserControl();
            purchasesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(purchasesForm);
        }

        private void LoadExpensesForm()
        {
            contentPanel.Controls.Clear();
            var expensesForm = new ExpensesUserControl();
            expensesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(expensesForm);
        }

        private void LoadReturnsForm()
        {
            contentPanel.Controls.Clear();
            var returnsForm = new ReturnsUserControl();
            returnsForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(returnsForm);
        }

        private void LoadInventoryForm()
        {
            contentPanel.Controls.Clear();
            var inventoryForm = new InventoryUserControl();
            inventoryForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(inventoryForm);
        }

        private void LoadReportsForm()
        {
            contentPanel.Controls.Clear();
            var reportsForm = new ReportsUserControl();
            reportsForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(reportsForm);
        }

        private void LoadSettingsForm()
        {
            contentPanel.Controls.Clear();
            var settingsForm = new SettingsUserControl();
            settingsForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(settingsForm);
        }

        private void SetActiveButton(Button activeButton)
        {
            // Reset all buttons
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button button && button != activeButton)
                {
                    button.BackColor = Color.FromArgb(52, 58, 64);
                    button.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
                }
            }

            // Set active button
            activeButton.BackColor = Color.FromArgb(0, 123, 255);
            activeButton.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
        }

        private void HeaderPanel_Paint(object sender, PaintEventArgs e)
        {
            // Create gradient background for header
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                headerPanel.ClientRectangle,
                Color.FromArgb(25, 118, 210),
                Color.FromArgb(13, 71, 161),
                System.Drawing.Drawing2D.LinearGradientMode.Horizontal))
            {
                e.Graphics.FillRectangle(brush, headerPanel.ClientRectangle);
            }
        }

        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // Add subtle border to side panel
            using (var pen = new Pen(Color.FromArgb(73, 80, 87), 1))
            {
                e.Graphics.DrawLine(pen, 0, 0, 0, sidePanel.Height);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Clean up advanced features
                AutoBackupManager.StopAutoBackup();
                SmartNotificationManager.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
