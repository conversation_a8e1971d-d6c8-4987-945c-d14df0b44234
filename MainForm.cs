using System;
using System.Drawing;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class MainForm : Form
    {
        private Panel sidePanel;
        private Panel contentPanel;
        private Panel headerPanel;
        private Label titleLabel;
        private Button salesButton;
        private Button purchasesButton;
        private Button expensesButton;
        private Button inventoryButton;
        private Button reportsButton;
        private Button settingsButton;

        public MainForm()
        {
            InitializeComponent();
            LoadDashboard();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "برنامج حسابات معرض دبي للموبايلات";
            this.Size = new Size(1200, 800);
            this.MinimumSize = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Header Panel
            headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(46, 134, 171)
            };

            titleLabel = new Label
            {
                Text = "معرض دبي للموبايلات - نظام إدارة المبيعات",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            headerPanel.Controls.Add(titleLabel);

            // Side Panel
            sidePanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 200,
                BackColor = Color.FromArgb(52, 58, 64)
            };

            CreateSideButtons();

            // Content Panel
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // Add controls to form
            this.Controls.Add(contentPanel);
            this.Controls.Add(sidePanel);
            this.Controls.Add(headerPanel);
        }

        private void CreateSideButtons()
        {
            var buttonHeight = 50;
            var buttonMargin = 5;
            var currentY = 20;

            // Sales Button
            salesButton = CreateSideButton("💰 المبيعات", currentY);
            salesButton.Click += (s, e) => LoadSalesForm();
            currentY += buttonHeight + buttonMargin;

            // Purchases Button
            purchasesButton = CreateSideButton("📦 المشتريات", currentY);
            purchasesButton.Click += (s, e) => LoadPurchasesForm();
            currentY += buttonHeight + buttonMargin;

            // Expenses Button
            expensesButton = CreateSideButton("💸 المصروفات", currentY);
            expensesButton.Click += (s, e) => LoadExpensesForm();
            currentY += buttonHeight + buttonMargin;

            // Inventory Button
            inventoryButton = CreateSideButton("📋 المخزون", currentY);
            inventoryButton.Click += (s, e) => LoadInventoryForm();
            currentY += buttonHeight + buttonMargin;

            // Reports Button
            reportsButton = CreateSideButton("📊 التقارير", currentY);
            reportsButton.Click += (s, e) => LoadReportsForm();
            currentY += buttonHeight + buttonMargin;

            // Settings Button
            settingsButton = CreateSideButton("⚙️ الإعدادات", currentY);
            settingsButton.Click += (s, e) => LoadSettingsForm();

            sidePanel.Controls.AddRange(new Control[] {
                salesButton, purchasesButton, expensesButton,
                inventoryButton, reportsButton, settingsButton
            });
        }

        private Button CreateSideButton(string text, int y)
        {
            return new Button
            {
                Text = text,
                Size = new Size(180, 45),
                Location = new Point(10, y),
                BackColor = Color.FromArgb(241, 143, 1),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand
            };
        }

        private void LoadDashboard()
        {
            contentPanel.Controls.Clear();

            var dashboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Welcome Label
            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام إدارة معرض دبي للموبايلات",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Statistics Panel
            var statsPanel = CreateStatisticsPanel();
            statsPanel.Location = new Point(20, 80);

            // Recent Activities Panel
            var activitiesPanel = CreateRecentActivitiesPanel();
            activitiesPanel.Location = new Point(20, 300);

            dashboardPanel.Controls.AddRange(new Control[] {
                welcomeLabel, statsPanel, activitiesPanel
            });

            contentPanel.Controls.Add(dashboardPanel);
        }

        private Panel CreateStatisticsPanel()
        {
            var panel = new Panel
            {
                Size = new Size(900, 200),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إحصائيات اليوم",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true
            };

            // Get today's statistics
            var stats = DatabaseManager.GetStatistics(DateTime.Today, DateTime.Today);

            var salesCard = CreateStatCard("المبيعات", stats.SalesCount.ToString(), 
                $"{stats.SalesTotal:C}", Color.FromArgb(40, 167, 69), 20, 50);

            var profitCard = CreateStatCard("صافي الربح", "", 
                $"{stats.NetProfit:C}", Color.FromArgb(23, 162, 184), 250, 50);

            panel.Controls.AddRange(new Control[] { titleLabel, salesCard, profitCard });

            return panel;
        }

        private Panel CreateStatCard(string title, string count, string amount, Color color, int x, int y)
        {
            var card = new Panel
            {
                Size = new Size(200, 120),
                Location = new Point(x, y),
                BackColor = color
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                AutoSize = true
            };

            var countLabel = new Label
            {
                Text = count,
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                AutoSize = true
            };

            var amountLabel = new Label
            {
                Text = amount,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(10, 85),
                AutoSize = true
            };

            card.Controls.AddRange(new Control[] { titleLabel, countLabel, amountLabel });

            return card;
        }

        private Panel CreateRecentActivitiesPanel()
        {
            var panel = new Panel
            {
                Size = new Size(900, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "الأنشطة الأخيرة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true
            };

            // Create DataGridView for recent sales
            var dataGridView = new DataGridView
            {
                Location = new Point(10, 40),
                Size = new Size(870, 240),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // Load recent sales
            var recentSales = DatabaseManager.GetSales(10);
            
            dataGridView.DataSource = recentSales;

            // Configure columns
            if (dataGridView.Columns.Count > 0)
            {
                dataGridView.Columns["Id"].HeaderText = "الرقم";
                dataGridView.Columns["CustomerName"].HeaderText = "العميل";
                dataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                dataGridView.Columns["Price"].HeaderText = "السعر";
                dataGridView.Columns["SaleDate"].HeaderText = "التاريخ";
                
                // Hide unnecessary columns
                dataGridView.Columns["CustomerPhone"].Visible = false;
                dataGridView.Columns["IMEI"].Visible = false;
                dataGridView.Columns["Seller"].Visible = false;
                dataGridView.Columns["CreatedAt"].Visible = false;
            }

            panel.Controls.AddRange(new Control[] { titleLabel, dataGridView });

            return panel;
        }

        private void LoadSalesForm()
        {
            contentPanel.Controls.Clear();
            var salesForm = new SalesUserControl();
            salesForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(salesForm);
        }

        private void LoadPurchasesForm()
        {
            MessageBox.Show("شاشة المشتريات قيد التطوير", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadExpensesForm()
        {
            MessageBox.Show("شاشة المصروفات قيد التطوير", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadInventoryForm()
        {
            MessageBox.Show("شاشة المخزون قيد التطوير", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadReportsForm()
        {
            MessageBox.Show("شاشة التقارير قيد التطوير", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadSettingsForm()
        {
            MessageBox.Show("شاشة الإعدادات قيد التطوير", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
