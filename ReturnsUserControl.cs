using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class ReturnsUserControl : UserControl
    {
        private Panel formPanel;
        private Panel listPanel;
        private TextBox customerNameTextBox;
        private TextBox customerPhoneTextBox;
        private TextBox deviceNameTextBox;
        private TextBox imeiTextBox;
        private TextBox amountTextBox;
        private ComboBox returnReasonComboBox;
        private TextBox customReasonTextBox;
        private TextBox notesTextBox;
        private DateTimePicker returnDatePicker;
        private Button addButton;
        private Button clearButton;
        private DataGridView returnsDataGridView;
        private TextBox searchTextBox;
        private Label totalReturnsLabel;

        public ReturnsUserControl()
        {
            InitializeComponent();
            LoadReturns();
            UpdateTotalReturns();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFormPanel();
            CreateListPanel();

            this.Controls.AddRange(new Control[] { formPanel, listPanel });
        }

        private void CreateFormPanel()
        {
            formPanel = new Panel
            {
                Size = new Size(980, 320),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إضافة مرتجع جديد",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            // Row 1
            var customerNameLabel = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            customerNameTextBox = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(200, 23)
            };

            var customerPhoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(350, 50),
                AutoSize = true
            };

            customerPhoneTextBox = new TextBox
            {
                Location = new Point(450, 47),
                Size = new Size(150, 23)
            };

            // Row 2
            var deviceNameLabel = new Label
            {
                Text = "اسم الجهاز:",
                Location = new Point(10, 90),
                AutoSize = true
            };

            deviceNameTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(200, 23)
            };

            var imeiLabel = new Label
            {
                Text = "رقم IMEI:",
                Location = new Point(350, 90),
                AutoSize = true
            };

            imeiTextBox = new TextBox
            {
                Location = new Point(450, 87),
                Size = new Size(200, 23)
            };

            // Row 3
            var amountLabel = new Label
            {
                Text = "المبلغ المرتجع (د.ع):",
                Location = new Point(10, 130),
                AutoSize = true
            };

            amountTextBox = new TextBox
            {
                Location = new Point(150, 127),
                Size = new Size(150, 23)
            };

            var returnDateLabel = new Label
            {
                Text = "تاريخ الإرجاع:",
                Location = new Point(330, 130),
                AutoSize = true
            };

            returnDatePicker = new DateTimePicker
            {
                Location = new Point(430, 127),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Row 4
            var returnReasonLabel = new Label
            {
                Text = "سبب الإرجاع:",
                Location = new Point(10, 170),
                AutoSize = true
            };

            returnReasonComboBox = new ComboBox
            {
                Location = new Point(120, 167),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            returnReasonComboBox.Items.AddRange(ReturnReasons.CommonReasons);
            returnReasonComboBox.SelectedIndexChanged += ReturnReasonComboBox_SelectedIndexChanged;

            var customReasonLabel = new Label
            {
                Text = "سبب آخر:",
                Location = new Point(350, 170),
                AutoSize = true
            };

            customReasonTextBox = new TextBox
            {
                Location = new Point(430, 167),
                Size = new Size(200, 23),
                Enabled = false
            };

            // Row 5
            var notesLabel = new Label
            {
                Text = "ملاحظات:",
                Location = new Point(10, 210),
                AutoSize = true
            };

            notesTextBox = new TextBox
            {
                Location = new Point(120, 207),
                Size = new Size(450, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Buttons
            addButton = new Button
            {
                Text = "إضافة المرتجع",
                Location = new Point(120, 280),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addButton.Click += AddButton_Click;

            clearButton = new Button
            {
                Text = "مسح الحقول",
                Location = new Point(260, 280),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            formPanel.Controls.AddRange(new Control[] {
                titleLabel, customerNameLabel, customerNameTextBox,
                customerPhoneLabel, customerPhoneTextBox, deviceNameLabel,
                deviceNameTextBox, imeiLabel, imeiTextBox, amountLabel,
                amountTextBox, returnDateLabel, returnDatePicker,
                returnReasonLabel, returnReasonComboBox, customReasonLabel,
                customReasonTextBox, notesLabel, notesTextBox,
                addButton, clearButton
            });
        }

        private void CreateListPanel()
        {
            listPanel = new Panel
            {
                Size = new Size(980, 350),
                Location = new Point(10, 340),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "قائمة المرتجعات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            totalReturnsLabel = new Label
            {
                Text = "إجمالي المرتجعات: 0 د.ع",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(600, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 45),
                AutoSize = true
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 42),
                Size = new Size(300, 23)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            returnsDataGridView = new DataGridView
            {
                Location = new Point(10, 75),
                Size = new Size(950, 260),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            listPanel.Controls.AddRange(new Control[] {
                titleLabel, totalReturnsLabel, searchLabel, searchTextBox, returnsDataGridView
            });
        }

        private void ReturnReasonComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool isOtherSelected = returnReasonComboBox.SelectedItem?.ToString() == "سبب آخر";
            customReasonTextBox.Enabled = isOtherSelected;
            if (isOtherSelected)
            {
                customReasonTextBox.Focus();
            }
            else
            {
                customReasonTextBox.Clear();
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    customerNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(deviceNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    deviceNameTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(amountTextBox.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    amountTextBox.Focus();
                    return;
                }

                string returnReason = "";
                if (returnReasonComboBox.SelectedItem?.ToString() == "سبب آخر")
                {
                    if (string.IsNullOrWhiteSpace(customReasonTextBox.Text))
                    {
                        MessageBox.Show("يرجى إدخال سبب الإرجاع", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        customReasonTextBox.Focus();
                        return;
                    }
                    returnReason = customReasonTextBox.Text.Trim();
                }
                else if (returnReasonComboBox.SelectedItem != null)
                {
                    returnReason = returnReasonComboBox.SelectedItem.ToString();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سبب الإرجاع", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    returnReasonComboBox.Focus();
                    return;
                }

                // Create return object
                var returnItem = new Return
                {
                    CustomerName = customerNameTextBox.Text.Trim(),
                    CustomerPhone = string.IsNullOrWhiteSpace(customerPhoneTextBox.Text) ? 
                        null : customerPhoneTextBox.Text.Trim(),
                    DeviceName = deviceNameTextBox.Text.Trim(),
                    IMEI = string.IsNullOrWhiteSpace(imeiTextBox.Text) ? 
                        null : imeiTextBox.Text.Trim(),
                    Amount = amount,
                    ReturnReason = returnReason,
                    Notes = string.IsNullOrWhiteSpace(notesTextBox.Text) ? 
                        null : notesTextBox.Text.Trim(),
                    ReturnDate = returnDatePicker.Value.Date
                };

                // Save to database
                var returnId = DatabaseManager.AddReturn(returnItem);

                MessageBox.Show($"تم إضافة المرتجع بنجاح!\nرقم العملية: {returnId}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear form and refresh list
                ClearForm();
                LoadReturns();
                UpdateTotalReturns();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المرتجع:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            customerNameTextBox.Clear();
            customerPhoneTextBox.Clear();
            deviceNameTextBox.Clear();
            imeiTextBox.Clear();
            amountTextBox.Clear();
            returnReasonComboBox.SelectedIndex = -1;
            customReasonTextBox.Clear();
            customReasonTextBox.Enabled = false;
            notesTextBox.Clear();
            returnDatePicker.Value = DateTime.Now;
            customerNameTextBox.Focus();
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            LoadReturns(searchTextBox.Text);
        }

        private void LoadReturns(string search = null)
        {
            try
            {
                var returns = DatabaseManager.GetReturns(50, search);
                returnsDataGridView.DataSource = returns;

                // Configure columns
                if (returnsDataGridView.Columns.Count > 0)
                {
                    returnsDataGridView.Columns["Id"].HeaderText = "الرقم";
                    returnsDataGridView.Columns["CustomerName"].HeaderText = "العميل";
                    returnsDataGridView.Columns["CustomerPhone"].HeaderText = "الهاتف";
                    returnsDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                    returnsDataGridView.Columns["IMEI"].HeaderText = "IMEI";
                    returnsDataGridView.Columns["Amount"].HeaderText = "المبلغ (د.ع)";
                    returnsDataGridView.Columns["ReturnReason"].HeaderText = "سبب الإرجاع";
                    returnsDataGridView.Columns["ReturnDate"].HeaderText = "التاريخ";
                    returnsDataGridView.Columns["Notes"].HeaderText = "ملاحظات";
                    
                    // Hide unnecessary columns
                    returnsDataGridView.Columns["OriginalSaleId"].Visible = false;
                    returnsDataGridView.Columns["CreatedAt"].Visible = false;

                    // Format columns
                    returnsDataGridView.Columns["Amount"].DefaultCellStyle.Format = "N0";
                    returnsDataGridView.Columns["ReturnDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المرتجعات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateTotalReturns()
        {
            try
            {
                var totalReturns = DatabaseManager.GetTotalReturns();
                totalReturnsLabel.Text = $"إجمالي المرتجعات: {totalReturns:N0} د.ع";
            }
            catch (Exception)
            {
                totalReturnsLabel.Text = "إجمالي المرتجعات: خطأ";
            }
        }
    }
}
