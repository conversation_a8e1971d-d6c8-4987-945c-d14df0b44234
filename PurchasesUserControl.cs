using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class PurchasesUserControl : UserControl
    {
        private Panel formPanel;
        private Panel listPanel;
        private TextBox supplierNameTextBox;
        private TextBox supplierPhoneTextBox;
        private TextBox deviceNameTextBox;
        private TextBox imeiTextBox;
        private TextBox priceTextBox;
        private TextBox sellingPriceTextBox;
        private ComboBox conditionComboBox;
        private DateTimePicker purchaseDatePicker;
        private Button addButton;
        private Button clearButton;
        private DataGridView purchasesDataGridView;
        private TextBox searchTextBox;

        public PurchasesUserControl()
        {
            InitializeComponent();
            LoadPurchases();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFormPanel();
            CreateListPanel();

            this.Controls.AddRange(new Control[] { formPanel, listPanel });
        }

        private void CreateFormPanel()
        {
            formPanel = new Panel
            {
                Size = new Size(980, 220),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إضافة عملية شراء جديدة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Row 1
            var supplierNameLabel = new Label
            {
                Text = "اسم المورد:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            supplierNameTextBox = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(200, 23)
            };

            var supplierPhoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(350, 50),
                AutoSize = true
            };

            supplierPhoneTextBox = new TextBox
            {
                Location = new Point(450, 47),
                Size = new Size(150, 23)
            };

            // Row 2
            var deviceNameLabel = new Label
            {
                Text = "اسم الجهاز:",
                Location = new Point(10, 90),
                AutoSize = true
            };

            deviceNameTextBox = new TextBox
            {
                Location = new Point(120, 87),
                Size = new Size(200, 23)
            };

            var imeiLabel = new Label
            {
                Text = "رقم IMEI:",
                Location = new Point(350, 90),
                AutoSize = true
            };

            imeiTextBox = new TextBox
            {
                Location = new Point(450, 87),
                Size = new Size(200, 23)
            };

            // Row 3
            var priceLabel = new Label
            {
                Text = "سعر الشراء (د.ع):",
                Location = new Point(10, 130),
                AutoSize = true
            };

            priceTextBox = new TextBox
            {
                Location = new Point(140, 127),
                Size = new Size(150, 23),
                PlaceholderText = "السعر بالآلاف (مثال: 400 = 400,000)"
            };
            priceTextBox.Leave += PriceTextBox_Leave;

            var purchaseDateLabel = new Label
            {
                Text = "تاريخ الشراء:",
                Location = new Point(320, 130),
                AutoSize = true
            };

            purchaseDatePicker = new DateTimePicker
            {
                Location = new Point(420, 127),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Row 4 - Additional fields for inventory
            var sellingPriceLabel = new Label
            {
                Text = "سعر البيع المقترح (د.إ):",
                Location = new Point(10, 170),
                AutoSize = true
            };

            sellingPriceTextBox = new TextBox
            {
                Location = new Point(170, 167),
                Size = new Size(150, 23),
                PlaceholderText = "سيتم حسابه تلقائياً"
            };

            var conditionLabel = new Label
            {
                Text = "حالة الجهاز:",
                Location = new Point(350, 170),
                AutoSize = true
            };

            conditionComboBox = new ComboBox
            {
                Location = new Point(450, 167),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            conditionComboBox.Items.AddRange(new string[] { "جديد", "مستعمل - ممتاز", "مستعمل - جيد", "يحتاج صيانة" });
            conditionComboBox.SelectedIndex = 0; // Default to "جديد"

            // Auto-calculate selling price when purchase price changes
            priceTextBox.TextChanged += (s, e) => {
                if (decimal.TryParse(priceTextBox.Text, out decimal purchasePrice) && purchasePrice > 0)
                {
                    var suggestedPrice = purchasePrice * 1.2m; // 20% markup
                    sellingPriceTextBox.Text = suggestedPrice.ToString("0.00");
                }
                else
                {
                    sellingPriceTextBox.Clear();
                }
            };

            // Buttons
            addButton = new Button
            {
                Text = "إضافة الشراء",
                Location = new Point(120, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addButton.Click += AddButton_Click;

            clearButton = new Button
            {
                Text = "مسح الحقول",
                Location = new Point(240, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            formPanel.Controls.AddRange(new Control[] {
                titleLabel, supplierNameLabel, supplierNameTextBox,
                supplierPhoneLabel, supplierPhoneTextBox, deviceNameLabel,
                deviceNameTextBox, imeiLabel, imeiTextBox, priceLabel,
                priceTextBox, purchaseDateLabel, purchaseDatePicker,
                addButton, clearButton
            });
        }

        private void CreateListPanel()
        {
            listPanel = new Panel
            {
                Size = new Size(980, 450),
                Location = new Point(10, 240),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "قائمة المشتريات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 45),
                AutoSize = true
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 42),
                Size = new Size(300, 23),
                PlaceholderText = "ابحث بالاسم أو IMEI أو رقم الهاتف..."
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            purchasesDataGridView = new DataGridView
            {
                Location = new Point(10, 75),
                Size = new Size(950, 360),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            listPanel.Controls.AddRange(new Control[] {
                titleLabel, searchLabel, searchTextBox, purchasesDataGridView
            });
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(supplierNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المورد", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    supplierNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(deviceNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    deviceNameTextBox.Focus();
                    return;
                }

                // Convert price input to thousands automatically
                decimal price = DatabaseManager.ConvertToThousands(priceTextBox.Text);
                if (price <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    priceTextBox.Focus();
                    return;
                }

                // Create purchase object
                var purchase = new Purchase
                {
                    SupplierName = supplierNameTextBox.Text.Trim(),
                    SupplierPhone = string.IsNullOrWhiteSpace(supplierPhoneTextBox.Text) ? 
                        null : supplierPhoneTextBox.Text.Trim(),
                    DeviceName = deviceNameTextBox.Text.Trim(),
                    IMEI = string.IsNullOrWhiteSpace(imeiTextBox.Text) ? 
                        null : imeiTextBox.Text.Trim(),
                    Price = price,
                    PurchaseDate = purchaseDatePicker.Value.Date
                };

                // Save to database
                var purchaseId = DatabaseManager.AddPurchase(purchase);

                try
                {
                    // Add to inventory automatically
                    DatabaseManager.AddPurchaseToInventory(purchase);

                    MessageBox.Show($"تم إضافة الشراء بنجاح!\nرقم العملية: {purchaseId}\n\n✅ تم إضافة الجهاز للمخزون تلقائياً", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception inventoryEx)
                {
                    MessageBox.Show($"تم إضافة الشراء بنجاح!\nرقم العملية: {purchaseId}\n\n⚠️ تحذير: حدث خطأ في إضافة الجهاز للمخزون:\n{inventoryEx.Message}\n\nيرجى إضافة الجهاز يدوياً في قسم المخزون.", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Clear form and refresh list
                ClearForm();
                LoadPurchases();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة الشراء:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void PriceTextBox_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(priceTextBox.Text))
            {
                // Convert to thousands and show formatted
                decimal price = DatabaseManager.ConvertToThousands(priceTextBox.Text);
                if (price > 0)
                {
                    // Show the input in thousands format
                    priceTextBox.Text = DatabaseManager.FormatPriceInput(price);
                    priceTextBox.BackColor = Color.LightGreen;

                    // Show tooltip with actual amount
                    var toolTip = new ToolTip();
                    toolTip.SetToolTip(priceTextBox, $"السعر الفعلي: {price:N0} د.ع");
                }
                else
                {
                    priceTextBox.BackColor = Color.LightCoral;
                }
            }
            else
            {
                priceTextBox.BackColor = Color.White;
            }
        }

        private void ClearForm()
        {
            supplierNameTextBox.Clear();
            supplierPhoneTextBox.Clear();
            deviceNameTextBox.Clear();
            imeiTextBox.Clear();
            priceTextBox.Clear();
            priceTextBox.BackColor = Color.White;
            purchaseDatePicker.Value = DateTime.Now;
            supplierNameTextBox.Focus();
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            LoadPurchases(searchTextBox.Text);
        }

        private void LoadPurchases(string search = null)
        {
            try
            {
                var purchases = DatabaseManager.GetPurchases(50, search);
                purchasesDataGridView.DataSource = purchases;

                // Configure columns
                if (purchasesDataGridView.Columns.Count > 0)
                {
                    purchasesDataGridView.Columns["Id"].HeaderText = "الرقم";
                    purchasesDataGridView.Columns["SupplierName"].HeaderText = "المورد";
                    purchasesDataGridView.Columns["SupplierPhone"].HeaderText = "الهاتف";
                    purchasesDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                    purchasesDataGridView.Columns["IMEI"].HeaderText = "IMEI";
                    purchasesDataGridView.Columns["Price"].HeaderText = "السعر (د.ع)";
                    purchasesDataGridView.Columns["PurchaseDate"].HeaderText = "التاريخ";
                    
                    // Hide CreatedAt column
                    purchasesDataGridView.Columns["CreatedAt"].Visible = false;

                    // Format price and date columns
                    purchasesDataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
                    purchasesDataGridView.Columns["PurchaseDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المشتريات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
