using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public partial class ReportsUserControl : UserControl
    {
        private Panel filterPanel;
        private Panel reportsPanel;
        private DateTimePicker startDatePicker;
        private DateTimePicker endDatePicker;
        private ComboBox reportTypeComboBox;
        private Button generateButton;
        private Button exportButton;
        private DataGridView reportsDataGridView;
        private Label summaryLabel;
        private Panel summaryPanel;

        public ReportsUserControl()
        {
            InitializeComponent();
            LoadDefaultReport();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateFilterPanel();
            CreateReportsPanel();

            this.Controls.AddRange(new Control[] { filterPanel, reportsPanel });
        }

        private void CreateFilterPanel()
        {
            filterPanel = new Panel
            {
                Size = new Size(980, 120),
                Location = new Point(10, 10),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "تقارير شاملة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(46, 134, 171)
            };

            // Date range
            var startDateLabel = new Label
            {
                Text = "من تاريخ:",
                Location = new Point(10, 50),
                AutoSize = true
            };

            startDatePicker = new DateTimePicker
            {
                Location = new Point(80, 47),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddDays(-30) // Last 30 days
            };

            var endDateLabel = new Label
            {
                Text = "إلى تاريخ:",
                Location = new Point(250, 50),
                AutoSize = true
            };

            endDatePicker = new DateTimePicker
            {
                Location = new Point(320, 47),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Report type
            var reportTypeLabel = new Label
            {
                Text = "نوع التقرير:",
                Location = new Point(500, 50),
                AutoSize = true
            };

            reportTypeComboBox = new ComboBox
            {
                Location = new Point(580, 47),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            reportTypeComboBox.Items.AddRange(new string[] {
                "تقرير شامل",
                "المبيعات فقط",
                "المشتريات فقط",
                "المصروفات فقط",
                "تقرير الأرباح",
                "تقرير المخزون"
            });
            reportTypeComboBox.SelectedIndex = 0;

            // Buttons
            generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Location = new Point(80, 80),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            generateButton.Click += GenerateButton_Click;

            exportButton = new Button
            {
                Text = "تصدير التقرير",
                Location = new Point(220, 80),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            exportButton.Click += ExportButton_Click;

            filterPanel.Controls.AddRange(new Control[] {
                titleLabel, startDateLabel, startDatePicker,
                endDateLabel, endDatePicker, reportTypeLabel,
                reportTypeComboBox, generateButton, exportButton
            });
        }

        private void CreateReportsPanel()
        {
            reportsPanel = new Panel
            {
                Size = new Size(980, 560),
                Location = new Point(10, 140),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Summary panel
            summaryPanel = new Panel
            {
                Size = new Size(960, 100),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            summaryLabel = new Label
            {
                Text = "ملخص التقرير",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(940, 80),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            summaryPanel.Controls.Add(summaryLabel);

            // Data grid
            reportsDataGridView = new DataGridView
            {
                Location = new Point(10, 120),
                Size = new Size(950, 430),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            reportsPanel.Controls.AddRange(new Control[] { summaryPanel, reportsDataGridView });
        }

        private void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                var startDate = startDatePicker.Value.Date;
                var endDate = endDatePicker.Value.Date;
                var reportType = reportTypeComboBox.SelectedItem?.ToString() ?? "تقرير شامل";

                GenerateReport(startDate, endDate, reportType);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (reportsDataGridView.DataSource == null)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv|Text Files (*.txt)|*.txt",
                    DefaultExt = "csv",
                    FileName = $"Report_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToFile(saveDialog.FileName);
                    MessageBox.Show($"تم تصدير التقرير بنجاح:\n{saveDialog.FileName}", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateReport(DateTime startDate, DateTime endDate, string reportType)
        {
            switch (reportType)
            {
                case "المبيعات فقط":
                    GenerateSalesReport(startDate, endDate);
                    break;
                case "المشتريات فقط":
                    GeneratePurchasesReport(startDate, endDate);
                    break;
                case "المصروفات فقط":
                    GenerateExpensesReport(startDate, endDate);
                    break;
                case "تقرير الأرباح":
                    GenerateProfitReport(startDate, endDate);
                    break;
                case "تقرير المخزون":
                    GenerateInventoryReport();
                    break;
                default:
                    GenerateComprehensiveReport(startDate, endDate);
                    break;
            }
        }

        private void GenerateSalesReport(DateTime startDate, DateTime endDate)
        {
            var sales = DatabaseManager.GetSales(null, null, startDate, endDate);
            reportsDataGridView.DataSource = sales;

            var totalSales = sales.Sum(s => s.Price);
            var avgSale = sales.Count > 0 ? sales.Average(s => s.Price) : 0;

            summaryLabel.Text = $@"تقرير المبيعات من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}
عدد المبيعات: {sales.Count}
إجمالي المبيعات: {totalSales:N0} د.ع
متوسط البيع: {avgSale:N0} د.ع";

            ConfigureSalesColumns();
        }

        private void GeneratePurchasesReport(DateTime startDate, DateTime endDate)
        {
            var purchases = DatabaseManager.GetPurchases(null, null, startDate, endDate);
            reportsDataGridView.DataSource = purchases;

            var totalPurchases = purchases.Sum(p => p.Price);
            var avgPurchase = purchases.Count > 0 ? purchases.Average(p => p.Price) : 0;

            summaryLabel.Text = $@"تقرير المشتريات من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}
عدد المشتريات: {purchases.Count}
إجمالي المشتريات: {totalPurchases:N0} د.ع
متوسط الشراء: {avgPurchase:N0} د.ع";

            ConfigurePurchasesColumns();
        }

        private void GenerateExpensesReport(DateTime startDate, DateTime endDate)
        {
            var expenses = DatabaseManager.GetExpenses(null, null, startDate, endDate);
            reportsDataGridView.DataSource = expenses;

            var totalExpenses = expenses.Sum(e => e.Amount);
            var avgExpense = expenses.Count > 0 ? expenses.Average(e => e.Amount) : 0;

            summaryLabel.Text = $@"تقرير المصروفات من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}
عدد المصروفات: {expenses.Count}
إجمالي المصروفات: {totalExpenses:N0} د.ع
متوسط المصروف: {avgExpense:N0} د.ع";

            ConfigureExpensesColumns();
        }

        private void GenerateProfitReport(DateTime startDate, DateTime endDate)
        {
            var stats = DatabaseManager.GetStatistics(startDate, endDate);
            
            // Create profit data for display
            var profitData = new[]
            {
                new { النوع = "المبيعات", العدد = stats.SalesCount, المبلغ = stats.SalesTotal },
                new { النوع = "المشتريات", العدد = stats.PurchasesCount, المبلغ = stats.PurchasesTotal },
                new { النوع = "المصروفات", العدد = stats.ExpensesCount, المبلغ = stats.ExpensesTotal },
                new { النوع = "صافي الربح", العدد = 0, المبلغ = stats.NetProfit }
            };

            reportsDataGridView.DataSource = profitData;

            var profitMargin = stats.SalesTotal > 0 ? (stats.NetProfit / stats.SalesTotal) * 100 : 0;

            summaryLabel.Text = $@"تقرير الأرباح من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}
إجمالي المبيعات: {stats.SalesTotal:N0} د.ع
إجمالي التكاليف: {(stats.PurchasesTotal + stats.ExpensesTotal):N0} د.ع
صافي الربح: {stats.NetProfit:N0} د.ع
هامش الربح: {profitMargin:F1}%";
        }

        private void GenerateInventoryReport()
        {
            var inventory = DatabaseManager.GetInventory();
            reportsDataGridView.DataSource = inventory;

            var summary = DatabaseManager.GetInventorySummary();

            summaryLabel.Text = $@"تقرير المخزون الحالي
إجمالي الأصناف: {summary.TotalItems}
قيمة المخزون: {summary.TotalValue:N0} د.ع
أصناف نفدت: {summary.LowStockItems}";

            ConfigureInventoryColumns();
        }

        private void GenerateComprehensiveReport(DateTime startDate, DateTime endDate)
        {
            var stats = DatabaseManager.GetStatistics(startDate, endDate);
            
            // Show sales data
            var sales = DatabaseManager.GetSales(20, null, startDate, endDate);
            reportsDataGridView.DataSource = sales;

            summaryLabel.Text = $@"التقرير الشامل من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}

المبيعات: {stats.SalesCount} عملية بقيمة {stats.SalesTotal:N0} د.ع
المشتريات: {stats.PurchasesCount} عملية بقيمة {stats.PurchasesTotal:N0} د.ع
المصروفات: {stats.ExpensesCount} عملية بقيمة {stats.ExpensesTotal:N0} د.ع
صافي الربح: {stats.NetProfit:N0} د.ع";

            ConfigureSalesColumns();
        }

        private void ConfigureSalesColumns()
        {
            if (reportsDataGridView.Columns.Count > 0)
            {
                reportsDataGridView.Columns["Id"].HeaderText = "الرقم";
                reportsDataGridView.Columns["CustomerName"].HeaderText = "العميل";
                reportsDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                reportsDataGridView.Columns["Price"].HeaderText = "السعر (د.ع)";
                reportsDataGridView.Columns["SaleDate"].HeaderText = "التاريخ";
                
                reportsDataGridView.Columns["CustomerPhone"].Visible = false;
                reportsDataGridView.Columns["IMEI"].Visible = false;
                reportsDataGridView.Columns["Seller"].Visible = false;
                reportsDataGridView.Columns["CreatedAt"].Visible = false;
                
                reportsDataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
                reportsDataGridView.Columns["SaleDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
        }

        private void ConfigurePurchasesColumns()
        {
            if (reportsDataGridView.Columns.Count > 0)
            {
                reportsDataGridView.Columns["Id"].HeaderText = "الرقم";
                reportsDataGridView.Columns["SupplierName"].HeaderText = "المورد";
                reportsDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                reportsDataGridView.Columns["Price"].HeaderText = "السعر (د.ع)";
                reportsDataGridView.Columns["PurchaseDate"].HeaderText = "التاريخ";
                
                reportsDataGridView.Columns["SupplierPhone"].Visible = false;
                reportsDataGridView.Columns["IMEI"].Visible = false;
                reportsDataGridView.Columns["CreatedAt"].Visible = false;
                
                reportsDataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
                reportsDataGridView.Columns["PurchaseDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
        }

        private void ConfigureExpensesColumns()
        {
            if (reportsDataGridView.Columns.Count > 0)
            {
                reportsDataGridView.Columns["Id"].HeaderText = "الرقم";
                reportsDataGridView.Columns["ExpenseType"].HeaderText = "نوع المصروف";
                reportsDataGridView.Columns["Amount"].HeaderText = "المبلغ (د.ع)";
                reportsDataGridView.Columns["ExpenseDate"].HeaderText = "التاريخ";
                reportsDataGridView.Columns["Notes"].HeaderText = "ملاحظات";
                
                reportsDataGridView.Columns["CreatedAt"].Visible = false;
                
                reportsDataGridView.Columns["Amount"].DefaultCellStyle.Format = "N0";
                reportsDataGridView.Columns["ExpenseDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
        }

        private void ConfigureInventoryColumns()
        {
            if (reportsDataGridView.Columns.Count > 0)
            {
                reportsDataGridView.Columns["Id"].HeaderText = "الرقم";
                reportsDataGridView.Columns["DeviceName"].HeaderText = "الجهاز";
                reportsDataGridView.Columns["Quantity"].HeaderText = "الكمية";
                reportsDataGridView.Columns["PurchasePrice"].HeaderText = "سعر الشراء (د.ع)";
                reportsDataGridView.Columns["SellingPrice"].HeaderText = "سعر البيع (د.ع)";
                reportsDataGridView.Columns["Status"].HeaderText = "الحالة";
                
                reportsDataGridView.Columns["IMEI"].Visible = false;
                reportsDataGridView.Columns["Condition"].Visible = false;
                reportsDataGridView.Columns["CreatedAt"].Visible = false;
                
                reportsDataGridView.Columns["PurchasePrice"].DefaultCellStyle.Format = "N0";
                reportsDataGridView.Columns["SellingPrice"].DefaultCellStyle.Format = "N0";
            }
        }

        private void ExportToFile(string fileName)
        {
            var sb = new StringBuilder();
            
            // Add summary
            sb.AppendLine(summaryLabel.Text);
            sb.AppendLine(new string('=', 50));
            sb.AppendLine();

            // Add headers
            var headers = reportsDataGridView.Columns.Cast<DataGridViewColumn>()
                .Where(c => c.Visible)
                .Select(c => c.HeaderText);
            sb.AppendLine(string.Join(",", headers));

            // Add data
            foreach (DataGridViewRow row in reportsDataGridView.Rows)
            {
                if (row.IsNewRow) continue;
                
                var values = reportsDataGridView.Columns.Cast<DataGridViewColumn>()
                    .Where(c => c.Visible)
                    .Select(c => row.Cells[c.Index].Value?.ToString() ?? "");
                sb.AppendLine(string.Join(",", values));
            }

            File.WriteAllText(fileName, sb.ToString(), Encoding.UTF8);
        }

        private void LoadDefaultReport()
        {
            GenerateComprehensiveReport(DateTime.Now.AddDays(-7), DateTime.Now);
        }
    }
}
