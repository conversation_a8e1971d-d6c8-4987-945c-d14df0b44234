using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public static class SmartNotificationManager
    {
        private static List<SmartNotification> notifications = new List<SmartNotification>();
        private static System.Windows.Forms.Timer notificationTimer;

        public static void Initialize()
        {
            notificationTimer = new System.Windows.Forms.Timer();
            notificationTimer.Interval = 60000; // Check every minute
            notificationTimer.Tick += CheckForNotifications;
            notificationTimer.Start();
        }

        private static void CheckForNotifications(object sender, EventArgs e)
        {
            try
            {
                CheckLowStock();
                CheckHighValueSales();
                CheckDailyBackup();
                CheckUnusualActivity();
            }
            catch (Exception ex)
            {
                // Log error silently
                System.IO.File.AppendAllText("notification_errors.log", 
                    $"{DateTime.Now}: {ex.Message}\n");
            }
        }

        private static void CheckLowStock()
        {
            try
            {
                var inventory = DatabaseManager.GetInventory();
                var lowStockItems = inventory.Where(i => i.Quantity <= 2 && i.Status == "متوفر").ToList();

                if (lowStockItems.Any())
                {
                    var message = $"تنبيه: {lowStockItems.Count} صنف بحاجة لإعادة تخزين";
                    var details = string.Join("\n", lowStockItems.Take(3).Select(i => $"• {i.DeviceName} (الكمية: {i.Quantity})"));
                    
                    AddNotification(new SmartNotification
                    {
                        Type = NotificationType.Warning,
                        Title = "مخزون منخفض",
                        Message = message,
                        Details = details,
                        ActionText = "عرض المخزون",
                        ActionCallback = () => ShowInventoryForm?.Invoke()
                    });
                }
            }
            catch { }
        }

        private static void CheckHighValueSales()
        {
            try
            {
                var todaySales = DatabaseManager.GetSales(null, null, DateTime.Today, DateTime.Today);
                var highValueSales = todaySales.Where(s => s.Price >= 5000).ToList();

                if (highValueSales.Any())
                {
                    var totalValue = highValueSales.Sum(s => s.Price);
                    var message = $"مبيعات عالية القيمة اليوم: {totalValue:N0} د.إ";
                    
                    AddNotification(new SmartNotification
                    {
                        Type = NotificationType.Success,
                        Title = "مبيعات ممتازة",
                        Message = message,
                        Details = $"عدد المبيعات: {highValueSales.Count}",
                        ActionText = "عرض المبيعات",
                        ActionCallback = () => ShowSalesForm?.Invoke()
                    });
                }
            }
            catch { }
        }

        private static void CheckDailyBackup()
        {
            try
            {
                var backupDir = AutoBackupManager.GetBackupDirectory();
                if (System.IO.Directory.Exists(backupDir))
                {
                    var todayBackups = System.IO.Directory.GetFiles(backupDir, "AutoBackup_*.db")
                        .Where(f => System.IO.File.GetCreationTime(f).Date == DateTime.Today)
                        .ToList();

                    if (!todayBackups.Any() && DateTime.Now.Hour >= 8)
                    {
                        AddNotification(new SmartNotification
                        {
                            Type = NotificationType.Info,
                            Title = "نسخة احتياطية",
                            Message = "لم يتم إنشاء نسخة احتياطية اليوم",
                            Details = "يُنصح بإنشاء نسخة احتياطية يومياً",
                            ActionText = "إنشاء نسخة",
                            ActionCallback = () => AutoBackupManager.CreateManualBackup("DailyBackup")
                        });
                    }
                }
            }
            catch { }
        }

        private static void CheckUnusualActivity()
        {
            try
            {
                var todaySales = DatabaseManager.GetSales(null, null, DateTime.Today, DateTime.Today);
                var todayReturns = DatabaseManager.GetReturns(null, null, DateTime.Today, DateTime.Today);

                // Check for high return rate
                if (todaySales.Count > 0 && todayReturns.Count > 0)
                {
                    var returnRate = (double)todayReturns.Count / todaySales.Count * 100;
                    if (returnRate > 20) // More than 20% return rate
                    {
                        AddNotification(new SmartNotification
                        {
                            Type = NotificationType.Warning,
                            Title = "معدل إرجاع مرتفع",
                            Message = $"معدل الإرجاع اليوم: {returnRate:F1}%",
                            Details = $"المبيعات: {todaySales.Count} | المرتجعات: {todayReturns.Count}",
                            ActionText = "عرض المرتجعات",
                            ActionCallback = () => ShowReturnsForm?.Invoke()
                        });
                    }
                }
            }
            catch { }
        }

        private static void AddNotification(SmartNotification notification)
        {
            // Avoid duplicate notifications
            if (notifications.Any(n => n.Title == notification.Title && 
                                     n.Message == notification.Message &&
                                     (DateTime.Now - n.CreatedAt).TotalHours < 1))
                return;

            notifications.Add(notification);
            ShowNotificationPopup(notification);

            // Keep only last 50 notifications
            if (notifications.Count > 50)
            {
                notifications.RemoveRange(0, notifications.Count - 50);
            }
        }

        private static void ShowNotificationPopup(SmartNotification notification)
        {
            var popup = new NotificationPopup(notification);
            popup.Show();
        }

        public static List<SmartNotification> GetRecentNotifications(int count = 10)
        {
            return notifications.OrderByDescending(n => n.CreatedAt).Take(count).ToList();
        }

        public static void ClearNotifications()
        {
            notifications.Clear();
        }

        // Events for navigation
        public static event Action ShowInventoryForm;
        public static event Action ShowSalesForm;
        public static event Action ShowReturnsForm;

        public static void Dispose()
        {
            notificationTimer?.Stop();
            notificationTimer?.Dispose();
        }
    }

    public class SmartNotification
    {
        public NotificationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string ActionText { get; set; } = string.Empty;
        public Action ActionCallback { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }

    public class NotificationPopup : Form
    {
        private SmartNotification notification;

        public NotificationPopup(SmartNotification notification)
        {
            this.notification = notification;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(350, 120);
            this.StartPosition = FormStartPosition.Manual;
            this.FormBorderStyle = FormBorderStyle.None;
            this.TopMost = true;
            this.ShowInTaskbar = false;

            // Position at bottom right of screen
            var screen = Screen.PrimaryScreen.WorkingArea;
            this.Location = new Point(screen.Right - this.Width - 10, screen.Bottom - this.Height - 10);

            // Set background color based on notification type
            Color bgColor = notification.Type switch
            {
                NotificationType.Success => Color.FromArgb(40, 167, 69),
                NotificationType.Warning => Color.FromArgb(255, 193, 7),
                NotificationType.Error => Color.FromArgb(220, 53, 69),
                _ => Color.FromArgb(23, 162, 184)
            };

            this.BackColor = bgColor;

            var titleLabel = new Label
            {
                Text = notification.Title,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                AutoSize = true
            };

            var messageLabel = new Label
            {
                Text = notification.Message,
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                Size = new Size(280, 40),
                AutoEllipsis = true
            };

            var closeButton = new Button
            {
                Text = "✕",
                Size = new Size(25, 25),
                Location = new Point(315, 5),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 8F, FontStyle.Bold)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] { titleLabel, messageLabel, closeButton });

            // Auto close after 5 seconds
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 5000;
            timer.Tick += (s, e) => { timer.Stop(); this.Close(); };
            timer.Start();

            // Click to perform action
            if (notification.ActionCallback != null)
            {
                this.Cursor = Cursors.Hand;
                this.Click += (s, e) => { notification.ActionCallback?.Invoke(); this.Close(); };
                titleLabel.Click += (s, e) => { notification.ActionCallback?.Invoke(); this.Close(); };
                messageLabel.Click += (s, e) => { notification.ActionCallback?.Invoke(); this.Close(); };
            }
        }
    }
}
