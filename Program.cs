using System;
using System.Windows.Forms;
using System.Globalization;
using System.Threading;

namespace DubaiMobileStore
{
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Set Arabic culture for right-to-left support
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = new CultureInfo("ar-SA");

            // Enable visual styles and DPI awareness
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.SetHighDpiMode(HighDpiMode.SystemAware);

            // Initialize database
            try
            {
                DatabaseManager.InitializeDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Run the main form
            Application.Run(new MainForm());
        }
    }
}
