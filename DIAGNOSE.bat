@echo off
setlocal enabledelayedexpansion
title System Diagnosis - Dubai Mobile Store

echo ==========================================
echo SYSTEM DIAGNOSIS
echo ==========================================
echo.

echo [CHECKING PYTHON INSTALLATION]
echo ==========================================

REM Check py command
echo Testing 'py' command:
py --version 2>nul
if !errorlevel! equ 0 (
    echo   SUCCESS: 'py' command works
    py --version
) else (
    echo   FAILED: 'py' command not found
)
echo.

REM Check python command
echo Testing 'python' command:
python --version 2>nul
if !errorlevel! equ 0 (
    echo   SUCCESS: 'python' command works
    python --version
) else (
    echo   FAILED: 'python' command not found
)
echo.

REM Check python3 command
echo Testing 'python3' command:
python3 --version 2>nul
if !errorlevel! equ 0 (
    echo   SUCCESS: 'python3' command works
    python3 --version
) else (
    echo   FAILED: 'python3' command not found
)
echo.

echo [CHECKING PYTHON PATHS]
echo ==========================================
echo Checking common Python installation paths:

for %%P in (
    "C:\Python39\python.exe"
    "C:\Python310\python.exe"
    "C:\Python311\python.exe"
    "C:\Python312\python.exe"
    "C:\Python313\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python39\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python310\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python311\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python312\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python313\python.exe"
) do (
    if exist %%P (
        echo   FOUND: %%P
        %%P --version 2>nul
    ) else (
        echo   NOT FOUND: %%P
    )
)
echo.

echo [CHECKING REQUIRED FILES]
echo ==========================================
set FILES_OK=1

for %%F in (
    "run_simple.py"
    "database.py"
    "models.py"
    "utils.py"
    "config.py"
    "main.py"
    "gui.py"
) do (
    if exist %%F (
        echo   FOUND: %%F
    ) else (
        echo   MISSING: %%F
        set FILES_OK=0
    )
)
echo.

echo [CHECKING DIRECTORIES]
echo ==========================================
for %%D in ("reports" "assets" "backups") do (
    if exist %%D (
        echo   EXISTS: %%D directory
    ) else (
        echo   MISSING: %%D directory (will be created)
    )
)
echo.

echo [TESTING BASIC PYTHON FUNCTIONALITY]
echo ==========================================

REM Try to run a simple Python test
echo Testing basic Python functionality...
echo print("Python test successful") > test_python.py

py test_python.py 2>nul
if !errorlevel! equ 0 (
    echo   SUCCESS: Python can execute scripts
) else (
    python test_python.py 2>nul
    if !errorlevel! equ 0 (
        echo   SUCCESS: Python can execute scripts
    ) else (
        echo   FAILED: Cannot execute Python scripts
    )
)

del test_python.py 2>nul
echo.

echo [DIAGNOSIS COMPLETE]
echo ==========================================
if !FILES_OK! equ 1 (
    echo STATUS: All required files found
) else (
    echo STATUS: Some files are missing
)
echo.
echo RECOMMENDATIONS:
echo 1. If Python is not found, install from https://python.org
echo 2. Make sure to check "Add Python to PATH" during installation
echo 3. If files are missing, make sure you're in the correct directory
echo 4. Try running LAUNCH.bat after fixing any issues
echo.
pause
