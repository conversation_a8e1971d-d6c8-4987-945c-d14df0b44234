@echo off
chcp 65001 > nul
title تثبيت المكتبات الاختيارية - معرض دبي للموبايلات

echo ========================================
echo تثبيت المكتبات الاختيارية
echo ========================================
echo.
echo هذه المكتبات اختيارية وتضيف مميزات متقدمة:
echo - reportlab: لإنشاء ملفات PDF
echo - openpyxl: لملفات Excel
echo - Pillow: لمعالجة الصور
echo - arabic-reshaper: لدعم النصوص العربية
echo - python-bidi: لاتجاه النص العربي
echo.

set /p choice="هل تريد تثبيت المكتبات الاختيارية؟ (y/n): "
if /i "%choice%" neq "y" (
    echo تم إلغاء التثبيت
    pause
    exit /b 0
)

echo.
echo جاري التحقق من Python...
py --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير متوفر
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✓ Python متوفر
echo.

echo جاري تثبيت المكتبات...
echo.

echo تثبيت reportlab...
py -m pip install reportlab
if %errorlevel% equ 0 (
    echo ✓ تم تثبيت reportlab بنجاح
) else (
    echo ✗ فشل في تثبيت reportlab
)

echo.
echo تثبيت openpyxl...
py -m pip install openpyxl
if %errorlevel% equ 0 (
    echo ✓ تم تثبيت openpyxl بنجاح
) else (
    echo ✗ فشل في تثبيت openpyxl
)

echo.
echo تثبيت Pillow...
py -m pip install Pillow
if %errorlevel% equ 0 (
    echo ✓ تم تثبيت Pillow بنجاح
) else (
    echo ✗ فشل في تثبيت Pillow
)

echo.
echo تثبيت arabic-reshaper...
py -m pip install arabic-reshaper
if %errorlevel% equ 0 (
    echo ✓ تم تثبيت arabic-reshaper بنجاح
) else (
    echo ✗ فشل في تثبيت arabic-reshaper
)

echo.
echo تثبيت python-bidi...
py -m pip install python-bidi
if %errorlevel% equ 0 (
    echo ✓ تم تثبيت python-bidi بنجاح
) else (
    echo ✗ فشل في تثبيت python-bidi
)

echo.
echo ========================================
echo انتهى التثبيت
echo ========================================
echo.
echo يمكنك الآن تشغيل البرنامج للاستفادة من المميزات المتقدمة
echo.

pause
