@echo off
title Create Desktop Shortcut

echo Creating desktop shortcut...

set "current_dir=%cd%"
set "desktop=%USERPROFILE%\Desktop"
set "shortcut_name=Dubai Mobile Store.lnk"

echo Current directory: %current_dir%
echo Desktop path: %desktop%

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\CreateShortcut.vbs"
echo sLinkFile = "%desktop%\%shortcut_name%" >> "%temp%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\CreateShortcut.vbs"
echo oLink.TargetPath = "%current_dir%\RUN.bat" >> "%temp%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%current_dir%" >> "%temp%\CreateShortcut.vbs"
echo oLink.Description = "Dubai Mobile Store Management System" >> "%temp%\CreateShortcut.vbs"
echo oLink.Save >> "%temp%\CreateShortcut.vbs"

REM Execute VBS script
cscript "%temp%\CreateShortcut.vbs" > nul

REM Clean up
del "%temp%\CreateShortcut.vbs"

if exist "%desktop%\%shortcut_name%" (
    echo SUCCESS: Shortcut created on desktop!
    echo You can now double-click "Dubai Mobile Store" on your desktop
) else (
    echo ERROR: Failed to create shortcut
)

echo.
pause
