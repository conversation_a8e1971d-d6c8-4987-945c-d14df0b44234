using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public static class ModernUIHelper
    {
        // Modern color palette
        public static class Colors
        {
            public static readonly Color Primary = Color.FromArgb(0, 123, 255);
            public static readonly Color Success = Color.FromArgb(40, 167, 69);
            public static readonly Color Warning = Color.FromArgb(255, 193, 7);
            public static readonly Color Danger = Color.FromArgb(220, 53, 69);
            public static readonly Color Info = Color.FromArgb(23, 162, 184);
            public static readonly Color Light = Color.FromArgb(248, 249, 250);
            public static readonly Color Dark = Color.FromArgb(33, 37, 41);
            public static readonly Color Secondary = Color.FromArgb(108, 117, 125);
            public static readonly Color White = Color.White;
            public static readonly Color Border = Color.FromArgb(222, 226, 230);
        }

        public static Button CreateModernButton(string text, Color backgroundColor, Color textColor, Size size)
        {
            var button = new Button
            {
                Text = text,
                Size = size,
                BackColor = backgroundColor,
                ForeColor = textColor,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.BorderColor = backgroundColor;

            // Add hover effects
            var originalColor = backgroundColor;
            var hoverColor = ControlPaint.Light(backgroundColor, 0.1f);
            var pressColor = ControlPaint.Dark(backgroundColor, 0.1f);

            button.MouseEnter += (s, e) => button.BackColor = hoverColor;
            button.MouseLeave += (s, e) => button.BackColor = originalColor;
            button.MouseDown += (s, e) => button.BackColor = pressColor;
            button.MouseUp += (s, e) => button.BackColor = hoverColor;

            return button;
        }

        public static Panel CreateModernCard(Size size, Point location)
        {
            var panel = new Panel
            {
                Size = size,
                Location = location,
                BackColor = Colors.White,
                Margin = new Padding(10)
            };

            panel.Paint += (s, e) => DrawCardShadow(e.Graphics, panel.ClientRectangle);
            return panel;
        }

        public static void DrawCardShadow(Graphics g, Rectangle bounds)
        {
            // Draw subtle shadow effect
            using (var shadowBrush = new SolidBrush(Color.FromArgb(15, 0, 0, 0)))
            {
                g.FillRectangle(shadowBrush, bounds.X + 3, bounds.Y + 3, bounds.Width, bounds.Height);
            }

            // Draw white background
            using (var backgroundBrush = new SolidBrush(Colors.White))
            {
                g.FillRectangle(backgroundBrush, bounds);
            }

            // Draw border
            using (var borderPen = new Pen(Colors.Border, 1))
            {
                g.DrawRectangle(borderPen, bounds.X, bounds.Y, bounds.Width - 1, bounds.Height - 1);
            }
        }

        public static void StyleDataGridView(DataGridView dgv)
        {
            // Basic styling
            dgv.BackgroundColor = Colors.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            dgv.GridColor = Colors.Border;
            dgv.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Header styling
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Colors.Light;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Colors.Dark;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 45;

            // Row styling
            dgv.DefaultCellStyle.BackColor = Colors.White;
            dgv.DefaultCellStyle.ForeColor = Colors.Dark;
            dgv.DefaultCellStyle.SelectionBackColor = Colors.Primary;
            dgv.DefaultCellStyle.SelectionForeColor = Colors.White;
            dgv.RowTemplate.Height = 40;

            // Alternating row colors
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Colors.Light;

            // Remove selection border
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(200, Colors.Primary);
        }

        public static TextBox CreateModernTextBox(Size size, Point location, string placeholder = "")
        {
            var textBox = new TextBox
            {
                Size = size,
                Location = location,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Colors.White,
                ForeColor = Colors.Dark
            };

            if (!string.IsNullOrEmpty(placeholder))
            {
                textBox.PlaceholderText = placeholder;
            }

            return textBox;
        }

        public static ComboBox CreateModernComboBox(Size size, Point location)
        {
            var comboBox = new ComboBox
            {
                Size = size,
                Location = location,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                BackColor = Colors.White,
                ForeColor = Colors.Dark,
                FlatStyle = FlatStyle.Flat,
                DropDownStyle = ComboBoxStyle.DropDown,
                AutoCompleteMode = AutoCompleteMode.SuggestAppend,
                AutoCompleteSource = AutoCompleteSource.ListItems
            };

            return comboBox;
        }

        public static Label CreateModernLabel(string text, Font font, Color color, Point location)
        {
            return new Label
            {
                Text = text,
                Font = font,
                ForeColor = color,
                Location = location,
                AutoSize = true
            };
        }

        public static Panel CreateFormSection(string title, Size size, Point location)
        {
            var panel = CreateModernCard(size, location);

            var titleLabel = CreateModernLabel(
                title,
                new Font("Segoe UI", 14F, FontStyle.Bold),
                Colors.Dark,
                new Point(20, 15)
            );

            panel.Controls.Add(titleLabel);
            return panel;
        }

        public static void AddGradientBackground(Panel panel, Color startColor, Color endColor)
        {
            panel.Paint += (s, e) =>
            {
                using (var brush = new LinearGradientBrush(
                    panel.ClientRectangle,
                    startColor,
                    endColor,
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, panel.ClientRectangle);
                }
            };
        }

        public static void AddHoverEffect(Control control, Color normalColor, Color hoverColor)
        {
            control.MouseEnter += (s, e) => control.BackColor = hoverColor;
            control.MouseLeave += (s, e) => control.BackColor = normalColor;
        }

        public static void ShowSuccessMessage(string message, string title = "نجح")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public static void ShowErrorMessage(string message, string title = "خطأ")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public static void ShowWarningMessage(string message, string title = "تحذير")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        public static DialogResult ShowConfirmDialog(string message, string title = "تأكيد")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        }
    }
}
