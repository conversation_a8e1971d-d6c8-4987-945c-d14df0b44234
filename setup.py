#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد وتثبيت برنامج حسابات معرض دبي للموبايلات
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['assets', 'reports', 'backups']
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            try:
                path.mkdir(parents=True, exist_ok=True)
                print(f"✅ تم إنشاء مجلد: {directory}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء مجلد {directory}: {e}")
                return False
        else:
            print(f"✅ مجلد موجود: {directory}")
    
    return True

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    try:
        print("🧪 اختبار الوظائف الأساسية...")
        
        # اختبار استيراد الوحدات
        from database import DatabaseManager
        from models import Sale, Purchase, Expense
        from utils import ReportGenerator
        
        print("✅ تم استيراد جميع الوحدات بنجاح")
        
        # اختبار قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار مولد التقارير
        report_gen = ReportGenerator()
        print("✅ تم تهيئة مولد التقارير بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف الأساسية: {e}")
        return False

def install_optional_packages():
    """تثبيت المكتبات الاختيارية"""
    optional_packages = [
        'reportlab',
        'openpyxl', 
        'Pillow',
        'arabic-reshaper',
        'python-bidi'
    ]
    
    print("📦 تثبيت المكتبات الاختيارية...")
    
    for package in optional_packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError:
            print(f"⚠️  فشل في تثبيت {package} (اختياري)")
        except Exception as e:
            print(f"⚠️  خطأ في تثبيت {package}: {e}")

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب (Windows فقط)"""
    if sys.platform != 'win32':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "معرض دبي للموبايلات.lnk")
        target = os.path.join(os.getcwd(), "START.bat")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
        
    except ImportError:
        print("⚠️  لم يتم إنشاء اختصار سطح المكتب (مكتبات مفقودة)")
    except Exception as e:
        print(f"⚠️  خطأ في إنشاء اختصار سطح المكتب: {e}")

def main():
    """الوظيفة الرئيسية للإعداد"""
    print("🚀 إعداد برنامج حسابات معرض دبي للموبايلات")
    print("=" * 50)
    
    # التحقق من Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء المجلدات
    if not create_directories():
        print("❌ فشل في إنشاء المجلدات المطلوبة")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار الوظائف الأساسية
    if not test_basic_functionality():
        print("❌ فشل في اختبار الوظائف الأساسية")
        input("اضغط Enter للخروج...")
        return
    
    # سؤال عن تثبيت المكتبات الاختيارية
    choice = input("\n📦 هل تريد تثبيت المكتبات الاختيارية؟ (y/n): ").strip().lower()
    if choice in ['y', 'yes', 'نعم', 'ن']:
        install_optional_packages()
    else:
        print("⏭️  تم تخطي تثبيت المكتبات الاختيارية")
        print("💡 يمكنك تثبيتها لاحقاً باستخدام install_optional.bat")
    
    # إنشاء اختصار سطح المكتب
    if sys.platform == 'win32':
        choice = input("\n🖥️  هل تريد إنشاء اختصار على سطح المكتب؟ (y/n): ").strip().lower()
        if choice in ['y', 'yes', 'نعم', 'ن']:
            create_desktop_shortcut()
    
    print("\n" + "=" * 50)
    print("✅ تم إعداد البرنامج بنجاح!")
    print("=" * 50)
    
    print("\n📋 طرق تشغيل البرنامج:")
    print("1. انقر نقراً مزدوجاً على START.bat")
    print("2. شغل: py main.py (للواجهة الرسومية)")
    print("3. شغل: py run_simple.py (للوضع النصي)")
    
    print("\n📚 للمساعدة:")
    print("- اقرأ ملف دليل_الاستخدام.md")
    print("- اقرأ ملف README.md")
    
    print("\n🎉 استمتع باستخدام البرنامج!")
    
    # سؤال عن تشغيل البرنامج
    choice = input("\n🚀 هل تريد تشغيل البرنامج الآن؟ (y/n): ").strip().lower()
    if choice in ['y', 'yes', 'نعم', 'ن']:
        try:
            # محاولة تشغيل الواجهة الرسومية
            subprocess.run([sys.executable, 'main.py'])
        except:
            # في حالة فشل الواجهة الرسومية، تشغيل الوضع النصي
            subprocess.run([sys.executable, 'run_simple.py'])

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nاضغط Enter للخروج...")
