# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات SQLite
"""

import sqlite3
import os
from typing import List, Optional, Dict, Any
from models import Sale, Purchase, Expense, Inventory, StoreSettings
from config import DATABASE_NAME, DEFAULT_STORE_NAME, DEFAULT_STORE_PHONE, DEFAULT_STORE_ADDRESS, DEFAULT_STORE_EMAIL

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_name: str = DATABASE_NAME):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_name)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # جدول المبيعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_name TEXT NOT NULL,
                    customer_phone TEXT,
                    device_name TEXT NOT NULL,
                    imei TEXT,
                    price REAL NOT NULL,
                    seller TEXT,
                    sale_date TEXT NOT NULL,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # جدول المشتريات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_name TEXT NOT NULL,
                    supplier_phone TEXT,
                    device_name TEXT NOT NULL,
                    imei TEXT,
                    price REAL NOT NULL,
                    purchase_date TEXT NOT NULL,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # جدول المصروفات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    expense_type TEXT NOT NULL,
                    amount REAL NOT NULL,
                    expense_date TEXT NOT NULL,
                    notes TEXT,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # جدول المخزون
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_name TEXT NOT NULL,
                    imei TEXT UNIQUE,
                    quantity INTEGER NOT NULL DEFAULT 1,
                    purchase_price REAL NOT NULL,
                    selling_price REAL NOT NULL,
                    condition TEXT NOT NULL DEFAULT 'جديد',
                    status TEXT NOT NULL DEFAULT 'متوفر',
                    created_at TEXT NOT NULL
                )
            ''')
            
            # جدول إعدادات المحل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS store_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    store_name TEXT NOT NULL,
                    phone TEXT,
                    address TEXT,
                    email TEXT,
                    logo_path TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            
            # إدراج الإعدادات الافتراضية إذا لم تكن موجودة
            self._insert_default_settings()
    
    def _insert_default_settings(self):
        """إدراج الإعدادات الافتراضية"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM store_settings")
            if cursor.fetchone()[0] == 0:
                settings = StoreSettings(
                    store_name=DEFAULT_STORE_NAME,
                    phone=DEFAULT_STORE_PHONE,
                    address=DEFAULT_STORE_ADDRESS,
                    email=DEFAULT_STORE_EMAIL
                )
                self.save_store_settings(settings)
    
    # عمليات المبيعات
    def add_sale(self, sale: Sale) -> int:
        """إضافة عملية بيع جديدة"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sales (customer_name, customer_phone, device_name, imei, 
                                 price, seller, sale_date, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (sale.customer_name, sale.customer_phone, sale.device_name, 
                  sale.imei, sale.price, sale.seller, sale.sale_date, sale.created_at))
            
            sale_id = cursor.lastrowid
            
            # تحديث حالة الجهاز في المخزون إلى "مباع"
            if sale.imei:
                cursor.execute('''
                    UPDATE inventory SET status = 'مباع' WHERE imei = ?
                ''', (sale.imei,))
            
            conn.commit()
            return sale_id
    
    def get_sales(self, limit: int = None, search: str = None, 
                  start_date: str = None, end_date: str = None) -> List[Sale]:
        """الحصول على قائمة المبيعات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM sales WHERE 1=1"
            params = []
            
            if search:
                query += " AND (customer_name LIKE ? OR device_name LIKE ? OR imei LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])
            
            if start_date:
                query += " AND sale_date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND sale_date <= ?"
                params.append(end_date)
            
            query += " ORDER BY created_at DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            return [Sale(
                id=row['id'],
                customer_name=row['customer_name'],
                customer_phone=row['customer_phone'],
                device_name=row['device_name'],
                imei=row['imei'],
                price=row['price'],
                seller=row['seller'],
                sale_date=row['sale_date'],
                created_at=row['created_at']
            ) for row in rows]

    # عمليات المشتريات
    def add_purchase(self, purchase: Purchase) -> int:
        """إضافة عملية شراء جديدة"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO purchases (supplier_name, supplier_phone, device_name, imei,
                                     price, purchase_date, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (purchase.supplier_name, purchase.supplier_phone, purchase.device_name,
                  purchase.imei, purchase.price, purchase.purchase_date, purchase.created_at))

            purchase_id = cursor.lastrowid

            # إضافة الجهاز إلى المخزون
            if purchase.imei:
                inventory_item = Inventory(
                    device_name=purchase.device_name,
                    imei=purchase.imei,
                    quantity=1,
                    purchase_price=purchase.price,
                    selling_price=purchase.price * 1.2,  # هامش ربح افتراضي 20%
                    condition="جديد",
                    status="متوفر"
                )
                self.add_inventory_item(inventory_item)

            conn.commit()
            return purchase_id

    def get_purchases(self, limit: int = None, search: str = None,
                     start_date: str = None, end_date: str = None) -> List[Purchase]:
        """الحصول على قائمة المشتريات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT * FROM purchases WHERE 1=1"
            params = []

            if search:
                query += " AND (supplier_name LIKE ? OR device_name LIKE ? OR imei LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            if start_date:
                query += " AND purchase_date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND purchase_date <= ?"
                params.append(end_date)

            query += " ORDER BY created_at DESC"

            if limit:
                query += f" LIMIT {limit}"

            cursor.execute(query, params)
            rows = cursor.fetchall()

            return [Purchase(
                id=row['id'],
                supplier_name=row['supplier_name'],
                supplier_phone=row['supplier_phone'],
                device_name=row['device_name'],
                imei=row['imei'],
                price=row['price'],
                purchase_date=row['purchase_date'],
                created_at=row['created_at']
            ) for row in rows]

    # عمليات المصروفات
    def add_expense(self, expense: Expense) -> int:
        """إضافة مصروف جديد"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO expenses (expense_type, amount, expense_date, notes, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (expense.expense_type, expense.amount, expense.expense_date,
                  expense.notes, expense.created_at))

            conn.commit()
            return cursor.lastrowid

    def get_expenses(self, limit: int = None, search: str = None,
                    start_date: str = None, end_date: str = None) -> List[Expense]:
        """الحصول على قائمة المصروفات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT * FROM expenses WHERE 1=1"
            params = []

            if search:
                query += " AND (expense_type LIKE ? OR notes LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param])

            if start_date:
                query += " AND expense_date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND expense_date <= ?"
                params.append(end_date)

            query += " ORDER BY created_at DESC"

            if limit:
                query += f" LIMIT {limit}"

            cursor.execute(query, params)
            rows = cursor.fetchall()

            return [Expense(
                id=row['id'],
                expense_type=row['expense_type'],
                amount=row['amount'],
                expense_date=row['expense_date'],
                notes=row['notes'],
                created_at=row['created_at']
            ) for row in rows]

    # عمليات المخزون
    def add_inventory_item(self, item: Inventory) -> int:
        """إضافة عنصر جديد للمخزون"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute('''
                    INSERT INTO inventory (device_name, imei, quantity, purchase_price,
                                         selling_price, condition, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (item.device_name, item.imei, item.quantity, item.purchase_price,
                      item.selling_price, item.condition, item.status, item.created_at))

                conn.commit()
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                # في حالة وجود IMEI مكرر، نحديث الكمية
                cursor.execute('''
                    UPDATE inventory SET quantity = quantity + ? WHERE imei = ?
                ''', (item.quantity, item.imei))
                conn.commit()
                return 0

    def get_inventory(self, search: str = None, status: str = None) -> List[Inventory]:
        """الحصول على قائمة المخزون"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT * FROM inventory WHERE 1=1"
            params = []

            if search:
                query += " AND (device_name LIKE ? OR imei LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param])

            if status:
                query += " AND status = ?"
                params.append(status)

            query += " ORDER BY created_at DESC"

            cursor.execute(query, params)
            rows = cursor.fetchall()

            return [Inventory(
                id=row['id'],
                device_name=row['device_name'],
                imei=row['imei'],
                quantity=row['quantity'],
                purchase_price=row['purchase_price'],
                selling_price=row['selling_price'],
                condition=row['condition'],
                status=row['status'],
                created_at=row['created_at']
            ) for row in rows]

    def update_inventory_item(self, item: Inventory) -> bool:
        """تحديث عنصر في المخزون"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE inventory SET device_name = ?, quantity = ?, purchase_price = ?,
                                   selling_price = ?, condition = ?, status = ?
                WHERE id = ?
            ''', (item.device_name, item.quantity, item.purchase_price,
                  item.selling_price, item.condition, item.status, item.id))

            conn.commit()
            return cursor.rowcount > 0

    def delete_inventory_item(self, item_id: int) -> bool:
        """حذف عنصر من المخزون"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM inventory WHERE id = ?", (item_id,))
            conn.commit()
            return cursor.rowcount > 0

    # عمليات إعدادات المحل
    def get_store_settings(self) -> Optional[StoreSettings]:
        """الحصول على إعدادات المحل"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM store_settings ORDER BY id DESC LIMIT 1")
            row = cursor.fetchone()

            if row:
                return StoreSettings(
                    id=row['id'],
                    store_name=row['store_name'],
                    phone=row['phone'],
                    address=row['address'],
                    email=row['email'],
                    logo_path=row['logo_path'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )
            return None

    def save_store_settings(self, settings: StoreSettings) -> int:
        """حفظ إعدادات المحل"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if settings.id:
                # تحديث الإعدادات الموجودة
                cursor.execute('''
                    UPDATE store_settings SET store_name = ?, phone = ?, address = ?,
                                            email = ?, logo_path = ?, updated_at = ?
                    WHERE id = ?
                ''', (settings.store_name, settings.phone, settings.address,
                      settings.email, settings.logo_path, settings.updated_at, settings.id))
            else:
                # إضافة إعدادات جديدة
                cursor.execute('''
                    INSERT INTO store_settings (store_name, phone, address, email,
                                              logo_path, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (settings.store_name, settings.phone, settings.address,
                      settings.email, settings.logo_path, settings.created_at, settings.updated_at))

            conn.commit()
            return cursor.lastrowid or settings.id

    # تقارير وإحصائيات
    def get_sales_summary(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """الحصول على ملخص المبيعات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT COUNT(*) as count, SUM(price) as total FROM sales WHERE 1=1"
            params = []

            if start_date:
                query += " AND sale_date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND sale_date <= ?"
                params.append(end_date)

            cursor.execute(query, params)
            result = cursor.fetchone()

            return {
                'count': result['count'] or 0,
                'total': result['total'] or 0.0
            }

    def get_purchases_summary(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """الحصول على ملخص المشتريات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT COUNT(*) as count, SUM(price) as total FROM purchases WHERE 1=1"
            params = []

            if start_date:
                query += " AND purchase_date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND purchase_date <= ?"
                params.append(end_date)

            cursor.execute(query, params)
            result = cursor.fetchone()

            return {
                'count': result['count'] or 0,
                'total': result['total'] or 0.0
            }

    def get_expenses_summary(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """الحصول على ملخص المصروفات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT COUNT(*) as count, SUM(amount) as total FROM expenses WHERE 1=1"
            params = []

            if start_date:
                query += " AND expense_date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND expense_date <= ?"
                params.append(end_date)

            cursor.execute(query, params)
            result = cursor.fetchone()

            return {
                'count': result['count'] or 0,
                'total': result['total'] or 0.0
            }

    def check_device_profit_warning(self, imei: str, selling_price: float) -> bool:
        """التحقق من تحذير الربح عند البيع بسعر أقل من سعر الشراء"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT purchase_price FROM inventory WHERE imei = ? AND status = 'متوفر'
            ''', (imei,))

            result = cursor.fetchone()
            if result and result['purchase_price'] > selling_price:
                return True
            return False
