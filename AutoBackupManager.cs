using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace DubaiMobileStore
{
    public static class AutoBackupManager
    {
        private static System.Windows.Forms.Timer backupTimer;
        private static readonly string BackupDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DubaiMobileStore", "AutoBackups");

        public static void StartAutoBackup()
        {
            try
            {
                // Create backup directory if it doesn't exist
                if (!Directory.Exists(BackupDirectory))
                    Directory.CreateDirectory(BackupDirectory);

                // Set up timer for daily backup at 2 AM
                backupTimer = new System.Windows.Forms.Timer();
                backupTimer.Interval = CalculateTimeToNextBackup();
                backupTimer.Tick += BackupTimer_Tick;
                backupTimer.Start();
            }
            catch (Exception ex)
            {
                // Log error but don't show to user
                File.WriteAllText(Path.Combine(BackupDirectory, "backup_error.log"), 
                    $"{DateTime.Now}: {ex.Message}");
            }
        }

        private static int CalculateTimeToNextBackup()
        {
            var now = DateTime.Now;
            var nextBackup = new DateTime(now.Year, now.Month, now.Day, 2, 0, 0);
            
            if (now > nextBackup)
                nextBackup = nextBackup.AddDays(1);

            return (int)(nextBackup - now).TotalMilliseconds;
        }

        private static void BackupTimer_Tick(object sender, EventArgs e)
        {
            Task.Run(() => PerformAutoBackup());
            
            // Reset timer for next day
            backupTimer.Interval = 24 * 60 * 60 * 1000; // 24 hours
        }

        private static void PerformAutoBackup()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var backupFileName = $"AutoBackup_{timestamp}.db";
                var backupPath = Path.Combine(BackupDirectory, backupFileName);

                // Copy database file
                var dbPath = DatabaseManager.GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    File.Copy(dbPath, backupPath, true);
                    
                    // Keep only last 30 backups
                    CleanOldBackups();
                    
                    // Log successful backup
                    var logPath = Path.Combine(BackupDirectory, "backup_log.txt");
                    File.AppendAllText(logPath, $"{DateTime.Now}: Auto backup created successfully - {backupFileName}\n");
                }
            }
            catch (Exception ex)
            {
                // Log error
                var errorLogPath = Path.Combine(BackupDirectory, "backup_error.log");
                File.AppendAllText(errorLogPath, $"{DateTime.Now}: Auto backup failed - {ex.Message}\n");
            }
        }

        private static void CleanOldBackups()
        {
            try
            {
                var backupFiles = Directory.GetFiles(BackupDirectory, "AutoBackup_*.db");
                if (backupFiles.Length > 30)
                {
                    Array.Sort(backupFiles);
                    for (int i = 0; i < backupFiles.Length - 30; i++)
                    {
                        File.Delete(backupFiles[i]);
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        public static void StopAutoBackup()
        {
            backupTimer?.Stop();
            backupTimer?.Dispose();
        }

        public static string GetBackupDirectory()
        {
            return BackupDirectory;
        }

        public static void CreateManualBackup(string customName = null)
        {
            try
            {
                if (!Directory.Exists(BackupDirectory))
                    Directory.CreateDirectory(BackupDirectory);

                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var backupFileName = string.IsNullOrEmpty(customName) 
                    ? $"ManualBackup_{timestamp}.db"
                    : $"{customName}_{timestamp}.db";
                var backupPath = Path.Combine(BackupDirectory, backupFileName);

                var dbPath = DatabaseManager.GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    File.Copy(dbPath, backupPath, true);
                    
                    MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح!\nالملف: {backupFileName}", 
                        "نسخة احتياطية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على قاعدة البيانات!", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في إنشاء النسخة الاحتياطية:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
